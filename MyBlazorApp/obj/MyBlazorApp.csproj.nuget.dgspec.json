{"format": 1, "restore": {"/home/<USER>/Documents/dot net 8/MyBlazorApp/MyBlazorApp.csproj": {}}, "projects": {"/home/<USER>/Documents/dot net 8/MyBlazorApp/MyBlazorApp.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "/home/<USER>/Documents/dot net 8/MyBlazorApp/MyBlazorApp.csproj", "projectName": "MyBlazorApp", "projectPath": "/home/<USER>/Documents/dot net 8/MyBlazorApp/MyBlazorApp.csproj", "packagesPath": "/home/<USER>/.nuget/packages/", "outputPath": "/home/<USER>/Documents/dot net 8/MyBlazorApp/obj/", "projectStyle": "PackageReference", "configFilePaths": ["/home/<USER>/.nuget/NuGet/NuGet.Config"], "originalTargetFrameworks": ["net8.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Microsoft.AspNetCore.Components.WebAssembly": {"target": "Package", "version": "[8.0.20, )"}, "Microsoft.AspNetCore.Components.WebAssembly.DevServer": {"suppressParent": "All", "target": "Package", "version": "[8.0.20, )"}, "Microsoft.NET.ILLink.Tasks": {"suppressParent": "All", "target": "Package", "version": "[8.0.20, )", "autoReferenced": true}, "Microsoft.NET.Sdk.WebAssembly.Pack": {"suppressParent": "All", "target": "Package", "version": "[8.0.20, )", "autoReferenced": true}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.NETCore.App.Runtime.Mono.browser-wasm", "version": "[8.0.20, 8.0.20]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "/usr/share/dotnet/sdk/8.0.414/PortableRuntimeIdentifierGraph.json"}}, "runtimes": {"browser-wasm": {"#import": []}}}}}