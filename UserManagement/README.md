# UserManagement - Modular Monolithic Architecture

A modern, enterprise-ready modular monolithic application built with .NET 8, Blazor WebAssembly, and Clean Architecture principles.

## 🏗️ Architecture Overview

This project demonstrates a **Modular Monolithic** architecture that provides:

- **Clean Architecture** with clear separation of concerns
- **Modular Design** for easy maintenance and testing
- **Microservices Migration Path** for future scalability
- **Blazor WebAssembly** for rich client-side experiences
- **CQRS Pattern** ready for complex business logic
- **Repository Pattern** for data access abstraction

## 📁 Project Structure

```
UserManagement/
├── UserManagement.sln                    # Solution file
├── create-modular-monolith.sh           # Project creation script
└── src/
    ├── UserManagement.Host/             # Main API project (.NET 8)
    │   ├── Controllers/                 # API Controllers
    │   ├── Middleware/                  # Custom middleware
    │   ├── Extensions/                  # Service registration extensions
    │   └── Program.cs                   # Application entry point
    │
    ├── UserManagement.Client/           # Blazor WebAssembly
    │   ├── Components/                  # Reusable components
    │   ├── Pages/                       # Application pages
    │   ├── Services/                    # Client services
    │   └── Program.cs                   # Client entry point
    │
    ├── UserManagement.Shared/           # Shared Kernel
    │   ├── Domain/                      # Base entities and common types
    │   ├── Application/                 # Common patterns and utilities
    │   └── Infrastructure/              # Shared infrastructure
    │
    └── Modules/                         # Business Modules
        ├── UserManagement.Modules.Users/     # User Management Module
        │   ├── Domain/                       # Entities, Value Objects, Interfaces
        │   ├── Application/                  # Commands, Queries, Handlers
        │   ├── Infrastructure/               # Repositories, Services
        │   └── Presentation/                 # Controllers
        │
        ├── UserManagement.Modules.Sites/     # Site Management Module
        ├── UserManagement.Modules.Applications/ # Application Management Module
        ├── UserManagement.Modules.Organizations/ # Organization Management Module
        └── UserManagement.Modules.Auth/      # Authentication Module
```

## 🚀 Quick Start

### Prerequisites

- .NET 8 SDK
- Visual Studio 2022 or VS Code
- Git

### 1. Create the Project

```bash
# Clone or download the create-modular-monolith.sh script
chmod +x create-modular-monolith.sh
./create-modular-monolith.sh
```

### 2. Build the Solution

```bash
cd UserManagement
dotnet build
```

### 3. Run the Application

```bash
# Terminal 1: Start the API server
dotnet run --project src/UserManagement.Host/UserManagement.Host

# Terminal 2: Start the Blazor client
dotnet run --project src/UserManagement.Client/UserManagement.Client
```

### 4. Access the Application

- **API**: https://localhost:7000
- **Blazor Client**: https://localhost:7001
- **Swagger UI**: https://localhost:7000/swagger

## 📚 API Endpoints

### Welcome Endpoints
- `GET /api/welcome` - Welcome message and system info
- `GET /api/welcome/system-info` - Detailed system information
- `GET /api/welcome/modules` - Available modules and their status

### User Management (Example Module)
- `GET /api/users` - Get all users
- `GET /api/users/{id}` - Get user by ID
- `POST /api/users` - Create new user
- `PUT /api/users/{id}` - Update user
- `DELETE /api/users/{id}` - Delete user

## 🏛️ Architecture Principles

### 1. Modular Monolithic Design
- Each module is self-contained with its own domain, application, and infrastructure layers
- Modules communicate through well-defined interfaces
- Easy to extract modules into microservices when needed

### 2. Clean Architecture
- **Domain Layer**: Core business logic and entities
- **Application Layer**: Use cases, commands, queries, and handlers
- **Infrastructure Layer**: Data access, external services
- **Presentation Layer**: Controllers, UI components

### 3. CQRS Pattern Ready
- Commands for write operations
- Queries for read operations
- Handlers for business logic
- Easy to add MediatR for advanced CQRS implementation

### 4. Repository Pattern
- Abstract data access layer
- Easy to switch between in-memory, SQL, NoSQL implementations
- Testable and maintainable

## 🔧 Technology Stack

- **.NET 8** - Latest .NET framework
- **ASP.NET Core** - Web API framework
- **Blazor WebAssembly** - Client-side web framework
- **Swagger/OpenAPI** - API documentation
- **CORS** - Cross-origin resource sharing
- **Dependency Injection** - Built-in IoC container

## 🎯 Benefits of This Architecture

### For Development
- **Team Scalability**: Different teams can work on different modules
- **Code Organization**: Clear separation of concerns
- **Testing**: Easy to unit test individual modules
- **Maintenance**: Changes in one module don't affect others

### For Business
- **Rapid Development**: Reusable components and patterns
- **Scalability**: Easy migration to microservices
- **Flexibility**: Add/remove modules as needed
- **Performance**: Optimized for both development and production

## 🚀 Migration to Microservices

This architecture is designed for easy migration to microservices:

1. **Extract Module**: Move a module to its own solution
2. **Add API Gateway**: Implement API gateway for routing
3. **Database Separation**: Move to separate databases
4. **Service Discovery**: Add service discovery mechanism
5. **Event Sourcing**: Implement event-driven communication

## 📖 Development Guidelines

### Adding a New Module

1. Create module structure in `src/Modules/`
2. Add domain entities and interfaces
3. Implement application layer (commands/queries)
4. Add infrastructure layer (repositories)
5. Create presentation layer (controllers)
6. Register services in `ServiceCollectionExtensions.cs`

### Code Organization

- **Entities**: Domain models with business logic
- **Interfaces**: Contracts for dependencies
- **Commands/Queries**: Application layer operations
- **Handlers**: Business logic implementation
- **Repositories**: Data access abstraction
- **Controllers**: API endpoints

## 🧪 Testing Strategy

- **Unit Tests**: Test individual components
- **Integration Tests**: Test module interactions
- **API Tests**: Test HTTP endpoints
- **End-to-End Tests**: Test complete user workflows

## 📈 Performance Considerations

- **Caching**: Implement caching strategies
- **Database Optimization**: Use proper indexing
- **API Optimization**: Implement pagination and filtering
- **Client Optimization**: Use Blazor WebAssembly efficiently

## 🔒 Security Features

- **CORS Configuration**: Proper cross-origin setup
- **Input Validation**: Model validation
- **Authentication Ready**: JWT authentication support
- **Authorization**: Role-based access control

## 📝 Contributing

1. Follow Clean Architecture principles
2. Maintain module independence
3. Write comprehensive tests
4. Document new features
5. Follow .NET coding standards

## 📄 License

This project is licensed under the MIT License.

## 🤝 Support

For questions and support, please create an issue in the repository.

---

**Built with ❤️ using .NET 8 and Modern Architecture Principles**
