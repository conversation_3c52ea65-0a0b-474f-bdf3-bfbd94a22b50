#!/bin/bash

# Create Modular Monolithic UserManagement Project - Complete Setup Script
echo "🚀 Creating Modular Monolithic UserManagement Project..."

# Step 1: Create solution and structure
mkdir UserManagement && cd UserManagement
dotnet new sln -n UserManagement
mkdir -p src/UserManagement.Host/{Controllers,Middleware,Extensions}
mkdir -p src/UserManagement.Client/{Components,Pages,Services}
mkdir -p src/UserManagement.Shared/{Domain,Application,Infrastructure}
mkdir -p src/Modules/UserManagement.Modules.Users/{Domain/{Entities,ValueObjects,Interfaces},Application/{Commands,Queries,Handlers,Interfaces},Infrastructure/{Repositories,Services},Presentation/Controllers}
mkdir -p src/Modules/UserManagement.Modules.Sites/{Domain/{Entities,ValueObjects,Interfaces},Application/{Commands,Queries,Handlers,Interfaces},Infrastructure/{Repositories,Services},Presentation/Controllers}
mkdir -p src/Modules/UserManagement.Modules.Applications/{Domain/{Entities,ValueObjects,Interfaces},Application/{Commands,Queries,Handlers,Interfaces},Infrastructure/{Repositories,Services},Presentation/Controllers}
mkdir -p src/Modules/UserManagement.Modules.Organizations/{Domain/{Entities,ValueObjects,Interfaces},Application/{Commands,Queries,Handlers,Interfaces},Infrastructure/{Repositories,Services},Presentation/Controllers}
mkdir -p src/Modules/UserManagement.Modules.Auth/{Domain/{Entities,ValueObjects,Interfaces},Application/{Commands,Queries,Handlers,Interfaces},Infrastructure/{Repositories,Services},Presentation/Controllers}

# Step 2: Create Host Project
cd src/UserManagement.Host
dotnet new webapi -n UserManagement.Host
cd UserManagement.Host
dotnet add package Swashbuckle.AspNetCore
cd ../..

# Step 3: Create Client Project
cd src/UserManagement.Client
dotnet new blazorwasm -n UserManagement.Client
cd ../..

# Step 4: Create Shared Project
cd src/UserManagement.Shared
dotnet new classlib -n UserManagement.Shared
cd ../..

# Step 5: Create Module Projects
cd src/Modules/UserManagement.Modules.Users
dotnet new classlib -n UserManagement.Modules.Users
dotnet add package Microsoft.AspNetCore.Mvc.Core
cd ../..

cd src/Modules/UserManagement.Modules.Sites
dotnet new classlib -n UserManagement.Modules.Sites
cd ../..

cd src/Modules/UserManagement.Modules.Applications
dotnet new classlib -n UserManagement.Modules.Applications
cd ../..

cd src/Modules/UserManagement.Modules.Organizations
dotnet new classlib -n UserManagement.Modules.Organizations
cd ../..

cd src/Modules/UserManagement.Modules.Auth
dotnet new classlib -n UserManagement.Modules.Auth
cd ../../..

# Step 6: Add references
cd src/UserManagement.Host/UserManagement.Host
dotnet add reference ../../UserManagement.Shared/UserManagement.Shared/UserManagement.Shared.csproj
dotnet add reference ../../Modules/UserManagement.Modules.Users/UserManagement.Modules.Users/UserManagement.Modules.Users.csproj
dotnet add reference ../../Modules/UserManagement.Modules.Sites/UserManagement.Modules.Sites/UserManagement.Modules.Sites.csproj
dotnet add reference ../../Modules/UserManagement.Modules.Applications/UserManagement.Modules.Applications/UserManagement.Modules.Applications.csproj
dotnet add reference ../../Modules/UserManagement.Modules.Organizations/UserManagement.Modules.Organizations/UserManagement.Modules.Organizations.csproj
dotnet add reference ../../Modules/UserManagement.Modules.Auth/UserManagement.Modules.Auth/UserManagement.Modules.Auth.csproj
cd ../../..

cd src/UserManagement.Client/UserManagement.Client
dotnet add reference ../../UserManagement.Shared/UserManagement.Shared/UserManagement.Shared.csproj
cd ../../..

cd src/Modules/UserManagement.Modules.Users/UserManagement.Modules.Users
dotnet add reference ../../../UserManagement.Shared/UserManagement.Shared/UserManagement.Shared.csproj
cd ../../..

cd src/Modules/UserManagement.Modules.Sites/UserManagement.Modules.Sites
dotnet add reference ../../../UserManagement.Shared/UserManagement.Shared/UserManagement.Shared.csproj
cd ../../..

cd src/Modules/UserManagement.Modules.Applications/UserManagement.Modules.Applications
dotnet add reference ../../../UserManagement.Shared/UserManagement.Shared/UserManagement.Shared.csproj
cd ../../..

cd src/Modules/UserManagement.Modules.Organizations/UserManagement.Modules.Organizations
dotnet add reference ../../../UserManagement.Shared/UserManagement.Shared/UserManagement.Shared.csproj
cd ../../..

cd src/Modules/UserManagement.Modules.Auth/UserManagement.Modules.Auth
dotnet add reference ../../../UserManagement.Shared/UserManagement.Shared/UserManagement.Shared.csproj
cd ../../..

# Step 7: Add to solution
dotnet sln add src/UserManagement.Host/UserManagement.Host/UserManagement.Host.csproj
dotnet sln add src/UserManagement.Client/UserManagement.Client/UserManagement.Client.csproj
dotnet sln add src/UserManagement.Shared/UserManagement.Shared/UserManagement.Shared.csproj
dotnet sln add src/Modules/UserManagement.Modules.Users/UserManagement.Modules.Users/UserManagement.Modules.Users.csproj
dotnet sln add src/Modules/UserManagement.Modules.Sites/UserManagement.Modules.Sites/UserManagement.Modules.Sites.csproj
dotnet sln add src/Modules/UserManagement.Modules.Applications/UserManagement.Modules.Applications/UserManagement.Modules.Applications.csproj
dotnet sln add src/Modules/UserManagement.Modules.Organizations/UserManagement.Modules.Organizations/UserManagement.Modules.Organizations.csproj
dotnet sln add src/Modules/UserManagement.Modules.Auth/UserManagement.Modules.Auth/UserManagement.Modules.Auth.csproj

echo "✅ Modular Monolithic Project structure created successfully!"
echo ""
echo "📁 Structure:"
echo "   ├── src/UserManagement.Host/          # API Server (.NET 8)"
echo "   │   ├── Controllers/                  # API Controllers"
echo "   │   ├── Middleware/                   # Custom middleware"
echo "   │   └── Extensions/                   # Service registration"
echo "   ├── src/UserManagement.Client/        # Blazor WebAssembly"
echo "   │   ├── Components/                   # Reusable components"
echo "   │   ├── Pages/                        # Application pages"
echo "   │   └── Services/                     # Client services"
echo "   ├── src/UserManagement.Shared/        # Shared Kernel"
echo "   │   ├── Domain/                       # Base entities"
echo "   │   ├── Application/                  # Common patterns"
echo "   │   └── Infrastructure/               # Shared infrastructure"
echo "   └── src/Modules/                      # Business Modules"
echo "       ├── UserManagement.Modules.Users/     # User Management"
echo "       ├── UserManagement.Modules.Sites/     # Site Management"
echo "       ├── UserManagement.Modules.Applications/ # Application Management"
echo "       ├── UserManagement.Modules.Organizations/ # Organization Management"
echo "       └── UserManagement.Modules.Auth/      # Authentication"
echo ""
echo "🏗️ Architecture Features:"
echo "   ✅ Modular Monolithic Design"
echo "   ✅ Clean Architecture (Domain, Application, Infrastructure, Presentation)"
echo "   ✅ CQRS Pattern Ready"
echo "   ✅ Repository Pattern"
echo "   ✅ Blazor WebAssembly"
echo "   ✅ Microservices Migration Path"
echo "   ✅ .NET 8 ASP.NET Core"
echo ""
echo "🚀 Next steps:"
echo "   1. cd UserManagement"
echo "   2. dotnet build"
echo "   3. dotnet run --project src/UserManagement.Host/UserManagement.Host"
echo "   4. dotnet run --project src/UserManagement.Client/UserManagement.Client"
echo ""
echo "📚 API Endpoints:"
echo "   • GET /api/welcome - Welcome message"
echo "   • GET /api/welcome/system-info - System information"
echo "   • GET /api/welcome/modules - Module information"
echo "   • GET /api/users - Users (when implemented)"
echo ""
echo "🎯 Ready for enterprise development!"
