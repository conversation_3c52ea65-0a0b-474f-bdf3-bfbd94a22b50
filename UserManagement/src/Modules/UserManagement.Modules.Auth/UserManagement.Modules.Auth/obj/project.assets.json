{"version": 3, "targets": {"net8.0": {"UserManagement.Shared/1.0.0": {"type": "project", "framework": ".NETCoreApp,Version=v8.0", "compile": {"bin/placeholder/UserManagement.Shared.dll": {}}, "runtime": {"bin/placeholder/UserManagement.Shared.dll": {}}}}}, "libraries": {"UserManagement.Shared/1.0.0": {"type": "project", "path": "../../../UserManagement.Shared/UserManagement.Shared/UserManagement.Shared.csproj", "msbuildProject": "../../../UserManagement.Shared/UserManagement.Shared/UserManagement.Shared.csproj"}}, "projectFileDependencyGroups": {"net8.0": ["UserManagement.Shared >= 1.0.0"]}, "packageFolders": {"/home/<USER>/.nuget/packages/": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "/home/<USER>/Documents/dot net 8/UserManagement/src/Modules/UserManagement.Modules.Auth/UserManagement.Modules.Auth/UserManagement.Modules.Auth.csproj", "projectName": "UserManagement.Modules.Auth", "projectPath": "/home/<USER>/Documents/dot net 8/UserManagement/src/Modules/UserManagement.Modules.Auth/UserManagement.Modules.Auth/UserManagement.Modules.Auth.csproj", "packagesPath": "/home/<USER>/.nuget/packages/", "outputPath": "/home/<USER>/Documents/dot net 8/UserManagement/src/Modules/UserManagement.Modules.Auth/UserManagement.Modules.Auth/obj/", "projectStyle": "PackageReference", "configFilePaths": ["/home/<USER>/.nuget/NuGet/NuGet.Config"], "originalTargetFrameworks": ["net8.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Shared/UserManagement.Shared/UserManagement.Shared.csproj": {"projectPath": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Shared/UserManagement.Shared/UserManagement.Shared.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "/usr/share/dotnet/sdk/8.0.414/PortableRuntimeIdentifierGraph.json"}}}}