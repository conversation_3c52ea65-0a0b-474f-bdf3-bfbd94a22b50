using UserManagement.Shared.Domain;

namespace UserManagement.Modules.Users.Domain.Entities;

/// <summary>
/// User entity
/// </summary>
public class User : BaseEntity
{
    /// <summary>
    /// Gets or sets the first name
    /// </summary>
    public string FirstName { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the last name
    /// </summary>
    public string LastName { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the email address
    /// </summary>
    public string Email { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the phone number
    /// </summary>
    public string? PhoneNumber { get; set; }

    /// <summary>
    /// Gets or sets the organization ID
    /// </summary>
    public Guid? OrganizationId { get; set; }

    /// <summary>
    /// Gets the full name
    /// </summary>
    public string FullName => $"{FirstName} {LastName}";
}
