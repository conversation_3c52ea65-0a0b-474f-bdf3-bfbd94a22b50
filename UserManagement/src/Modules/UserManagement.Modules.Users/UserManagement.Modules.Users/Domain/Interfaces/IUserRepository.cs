using UserManagement.Modules.Users.Domain.Entities;

namespace UserManagement.Modules.Users.Domain.Interfaces;

/// <summary>
/// User repository interface
/// </summary>
public interface IUserRepository
{
    /// <summary>
    /// Gets all users
    /// </summary>
    /// <returns>List of users</returns>
    Task<IEnumerable<User>> GetAllAsync();

    /// <summary>
    /// Gets a user by ID
    /// </summary>
    /// <param name="id">User ID</param>
    /// <returns>User or null</returns>
    Task<User?> GetByIdAsync(Guid id);

    /// <summary>
    /// Gets a user by email
    /// </summary>
    /// <param name="email">Email address</param>
    /// <returns>User or null</returns>
    Task<User?> GetByEmailAsync(string email);

    /// <summary>
    /// Creates a new user
    /// </summary>
    /// <param name="user">User to create</param>
    /// <returns>Created user</returns>
    Task<User> CreateAsync(User user);

    /// <summary>
    /// Updates an existing user
    /// </summary>
    /// <param name="user">User to update</param>
    /// <returns>Updated user</returns>
    Task<User> UpdateAsync(User user);

    /// <summary>
    /// Deletes a user
    /// </summary>
    /// <param name="id">User ID</param>
    /// <returns>True if deleted</returns>
    Task<bool> DeleteAsync(Guid id);
}
