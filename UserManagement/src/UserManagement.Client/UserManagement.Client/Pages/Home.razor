﻿@page "/"
@inject HttpClient Http

<PageTitle>User Management System</PageTitle>

<div class="hero-section">
    <div class="container">
        <h1 class="display-4">Welcome to User Management System</h1>
        <p class="lead">A Modern Modular Monolithic Architecture</p>
        <div class="feature-grid">
            <div class="feature-card">
                <h3>🏗️ Modular Architecture</h3>
                <p>Clean separation of concerns with independent modules</p>
            </div>
            <div class="feature-card">
                <h3>⚡ Blazor WebAssembly</h3>
                <p>Fast, interactive client-side web applications</p>
            </div>
            <div class="feature-card">
                <h3>🔧 Microservices Ready</h3>
                <p>Easy migration path to microservices architecture</p>
            </div>
            <div class="feature-card">
                <h3>📊 Clean Architecture</h3>
                <p>Domain-driven design with CQRS pattern</p>
            </div>
        </div>
    </div>
</div>

<div class="modules-section">
    <div class="container">
        <h2>Available Modules</h2>
        <div class="modules-grid">
            <div class="module-card">
                <h4>👥 Users</h4>
                <p>User management and profiles</p>
                <button class="btn btn-primary" @onclick="LoadUsers">View Users</button>
            </div>
            <div class="module-card">
                <h4>🏢 Sites</h4>
                <p>Site and location management</p>
                <button class="btn btn-secondary" disabled>Coming Soon</button>
            </div>
            <div class="module-card">
                <h4>📱 Applications</h4>
                <p>Application and service management</p>
                <button class="btn btn-secondary" disabled>Coming Soon</button>
            </div>
            <div class="module-card">
                <h4>🏛️ Organizations</h4>
                <p>Organization and company management</p>
                <button class="btn btn-secondary" disabled>Coming Soon</button>
            </div>
            <div class="module-card">
                <h4>🔐 Authentication</h4>
                <p>Security and access control</p>
                <button class="btn btn-secondary" disabled>Coming Soon</button>
            </div>
        </div>
    </div>
</div>

@if (users != null && users.Any())
{
    <div class="users-section">
        <div class="container">
            <h2>Users</h2>
            <div class="users-grid">
                @foreach (var user in users)
                {
                    <div class="user-card">
                        <h5>@user.FullName</h5>
                        <p>📧 @user.Email</p>
                        @if (!string.IsNullOrEmpty(user.PhoneNumber))
                        {
                            <p>📞 @user.PhoneNumber</p>
                        }
                        <small>Created: @user.CreatedAt.ToString("MMM dd, yyyy")</small>
                    </div>
                }
            </div>
        </div>
    </div>
}

@code {
    private List<User>? users;

    private async Task LoadUsers()
    {
        try
        {
            users = await Http.GetFromJsonAsync<List<User>>("api/users");
        }
        catch (Exception ex)
        {
            // Handle error - in a real app, you'd show a proper error message
            Console.WriteLine($"Error loading users: {ex.Message}");
        }
    }

    public class User
    {
        public Guid Id { get; set; }
        public string FirstName { get; set; } = string.Empty;
        public string LastName { get; set; } = string.Empty;
        public string Email { get; set; } = string.Empty;
        public string? PhoneNumber { get; set; }
        public DateTime CreatedAt { get; set; }
        public string FullName => $"{FirstName} {LastName}";
    }
}

<style>
    .hero-section {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 4rem 0;
        text-align: center;
    }

    .container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 0 1rem;
    }

    .display-4 {
        font-size: 3.5rem;
        font-weight: 300;
        margin-bottom: 1rem;
    }

    .lead {
        font-size: 1.25rem;
        margin-bottom: 3rem;
        opacity: 0.9;
    }

    .feature-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 2rem;
        margin-top: 3rem;
    }

    .feature-card {
        background: rgba(255, 255, 255, 0.1);
        padding: 2rem;
        border-radius: 10px;
        backdrop-filter: blur(10px);
    }

    .feature-card h3 {
        margin-bottom: 1rem;
        font-size: 1.5rem;
    }

    .modules-section {
        padding: 4rem 0;
        background: #f8f9fa;
    }

    .modules-section h2 {
        text-align: center;
        margin-bottom: 3rem;
        color: #333;
    }

    .modules-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 2rem;
    }

    .module-card {
        background: white;
        padding: 2rem;
        border-radius: 10px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        text-align: center;
        transition: transform 0.3s ease;
    }

    .module-card:hover {
        transform: translateY(-5px);
    }

    .module-card h4 {
        margin-bottom: 1rem;
        color: #333;
    }

    .users-section {
        padding: 4rem 0;
        background: white;
    }

    .users-section h2 {
        text-align: center;
        margin-bottom: 3rem;
        color: #333;
    }

    .users-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 2rem;
    }

    .user-card {
        background: #f8f9fa;
        padding: 1.5rem;
        border-radius: 10px;
        border-left: 4px solid #667eea;
    }

    .user-card h5 {
        margin-bottom: 1rem;
        color: #333;
    }

    .user-card p {
        margin-bottom: 0.5rem;
        color: #666;
    }

    .user-card small {
        color: #999;
    }

    .btn {
        padding: 0.75rem 1.5rem;
        border: none;
        border-radius: 5px;
        cursor: pointer;
        text-decoration: none;
        display: inline-block;
        transition: background-color 0.3s ease;
    }

    .btn-primary {
        background: #667eea;
        color: white;
    }

    .btn-primary:hover {
        background: #5a6fd8;
    }

    .btn-secondary {
        background: #6c757d;
        color: white;
    }

    .btn-secondary:disabled {
        background: #dee2e6;
        color: #6c757d;
        cursor: not-allowed;
    }
</style>