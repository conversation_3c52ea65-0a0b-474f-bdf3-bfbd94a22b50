{"Version": 1, "Hash": "Zn0l/kkTMZbXmSQswgvzk5YYP2WFwz7b9ESlebRwOZc=", "Source": "UserManagement.Client", "BasePath": "/", "Mode": "Root", "ManifestType": "Build", "ReferencedProjectsConfiguration": [], "DiscoveryPatterns": [{"Name": "UserManagement.Client/wwwroot", "Source": "UserManagement.Client", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/wwwroot/", "BasePath": "/", "Pattern": "**"}], "Assets": [{"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/blazor.boot.json", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/blazor.boot.json", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "WasmResource", "AssetTraitValue": "manifest", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "obj/Debug/net8.0/blazor.boot.json"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/blazor.webassembly.js", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/blazor.webassembly.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "WasmResource", "AssetTraitValue": "boot", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/home/<USER>/.nuget/packages/microsoft.aspnetcore.components.webassembly/8.0.20/build/net8.0/blazor.webassembly.js"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/dotnet.js", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/dotnet.js", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "WasmResource", "AssetTraitValue": "native", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/.nuget/packages/microsoft.netcore.app.runtime.mono.browser-wasm/8.0.20/runtimes/browser-wasm/native/dotnet.js"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/dotnet.js.map", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/dotnet.js.map", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/.nuget/packages/microsoft.netcore.app.runtime.mono.browser-wasm/8.0.20/runtimes/browser-wasm/native/dotnet.js.map"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/dotnet.native.8.0.20.frvb3disbh.js", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/dotnet.native.8.0.20.frvb3disbh.js", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "WasmResource", "AssetTraitValue": "native", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/.nuget/packages/microsoft.netcore.app.runtime.mono.browser-wasm/8.0.20/runtimes/browser-wasm/native/dotnet.native.js"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/dotnet.native.wasm", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/dotnet.native.wasm", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "WasmResource", "AssetTraitValue": "native", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/.nuget/packages/microsoft.netcore.app.runtime.mono.browser-wasm/8.0.20/runtimes/browser-wasm/native/dotnet.native.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/dotnet.runtime.8.0.20.gped7iqcy4.js", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/dotnet.runtime.8.0.20.gped7iqcy4.js", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "WasmResource", "AssetTraitValue": "native", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/.nuget/packages/microsoft.netcore.app.runtime.mono.browser-wasm/8.0.20/runtimes/browser-wasm/native/dotnet.runtime.js"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/dotnet.runtime.js.map", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/dotnet.runtime.js.map", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/.nuget/packages/microsoft.netcore.app.runtime.mono.browser-wasm/8.0.20/runtimes/browser-wasm/native/dotnet.runtime.js.map"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/icudt_CJK.dat", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/icudt_CJK.dat", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "WasmResource", "AssetTraitValue": "native", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/.nuget/packages/microsoft.netcore.app.runtime.mono.browser-wasm/8.0.20/runtimes/browser-wasm/native/icudt_CJK.dat"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/icudt_EFIGS.dat", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/icudt_EFIGS.dat", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "WasmResource", "AssetTraitValue": "native", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/.nuget/packages/microsoft.netcore.app.runtime.mono.browser-wasm/8.0.20/runtimes/browser-wasm/native/icudt_EFIGS.dat"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/icudt_no_CJK.dat", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/icudt_no_CJK.dat", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "WasmResource", "AssetTraitValue": "native", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/.nuget/packages/microsoft.netcore.app.runtime.mono.browser-wasm/8.0.20/runtimes/browser-wasm/native/icudt_no_CJK.dat"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/Microsoft.AspNetCore.Authorization.wasm", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/Microsoft.AspNetCore.Authorization.wasm", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "WasmResource", "AssetTraitValue": "runtime", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/webcil/Microsoft.AspNetCore.Authorization.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/Microsoft.AspNetCore.Components.Forms.wasm", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/Microsoft.AspNetCore.Components.Forms.wasm", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "WasmResource", "AssetTraitValue": "runtime", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/webcil/Microsoft.AspNetCore.Components.Forms.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/Microsoft.AspNetCore.Components.wasm", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/Microsoft.AspNetCore.Components.wasm", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "WasmResource", "AssetTraitValue": "runtime", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/webcil/Microsoft.AspNetCore.Components.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/Microsoft.AspNetCore.Components.Web.wasm", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/Microsoft.AspNetCore.Components.Web.wasm", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "WasmResource", "AssetTraitValue": "runtime", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/webcil/Microsoft.AspNetCore.Components.Web.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/Microsoft.AspNetCore.Components.WebAssembly.wasm", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/Microsoft.AspNetCore.Components.WebAssembly.wasm", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "WasmResource", "AssetTraitValue": "runtime", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/webcil/Microsoft.AspNetCore.Components.WebAssembly.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/Microsoft.AspNetCore.Metadata.wasm", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/Microsoft.AspNetCore.Metadata.wasm", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "WasmResource", "AssetTraitValue": "runtime", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/webcil/Microsoft.AspNetCore.Metadata.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/Microsoft.CSharp.wasm", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/Microsoft.CSharp.wasm", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "WasmResource", "AssetTraitValue": "runtime", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/webcil/Microsoft.CSharp.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/Microsoft.Extensions.Configuration.Abstractions.wasm", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.Configuration.Abstractions.wasm", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "WasmResource", "AssetTraitValue": "runtime", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/webcil/Microsoft.Extensions.Configuration.Abstractions.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/Microsoft.Extensions.Configuration.Binder.wasm", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.Configuration.Binder.wasm", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "WasmResource", "AssetTraitValue": "runtime", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/webcil/Microsoft.Extensions.Configuration.Binder.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/Microsoft.Extensions.Configuration.FileExtensions.wasm", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.Configuration.FileExtensions.wasm", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "WasmResource", "AssetTraitValue": "runtime", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/webcil/Microsoft.Extensions.Configuration.FileExtensions.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/Microsoft.Extensions.Configuration.Json.wasm", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.Configuration.Json.wasm", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "WasmResource", "AssetTraitValue": "runtime", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/webcil/Microsoft.Extensions.Configuration.Json.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/Microsoft.Extensions.Configuration.wasm", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.Configuration.wasm", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "WasmResource", "AssetTraitValue": "runtime", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/webcil/Microsoft.Extensions.Configuration.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/Microsoft.Extensions.DependencyInjection.Abstractions.wasm", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.DependencyInjection.Abstractions.wasm", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "WasmResource", "AssetTraitValue": "runtime", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/webcil/Microsoft.Extensions.DependencyInjection.Abstractions.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/Microsoft.Extensions.DependencyInjection.wasm", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.DependencyInjection.wasm", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "WasmResource", "AssetTraitValue": "runtime", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/webcil/Microsoft.Extensions.DependencyInjection.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/Microsoft.Extensions.FileProviders.Abstractions.wasm", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.FileProviders.Abstractions.wasm", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "WasmResource", "AssetTraitValue": "runtime", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/webcil/Microsoft.Extensions.FileProviders.Abstractions.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/Microsoft.Extensions.FileProviders.Physical.wasm", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.FileProviders.Physical.wasm", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "WasmResource", "AssetTraitValue": "runtime", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/webcil/Microsoft.Extensions.FileProviders.Physical.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/Microsoft.Extensions.FileSystemGlobbing.wasm", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.FileSystemGlobbing.wasm", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "WasmResource", "AssetTraitValue": "runtime", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/webcil/Microsoft.Extensions.FileSystemGlobbing.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/Microsoft.Extensions.Logging.Abstractions.wasm", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.Logging.Abstractions.wasm", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "WasmResource", "AssetTraitValue": "runtime", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/webcil/Microsoft.Extensions.Logging.Abstractions.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/Microsoft.Extensions.Logging.wasm", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.Logging.wasm", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "WasmResource", "AssetTraitValue": "runtime", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/webcil/Microsoft.Extensions.Logging.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/Microsoft.Extensions.Options.wasm", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.Options.wasm", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "WasmResource", "AssetTraitValue": "runtime", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/webcil/Microsoft.Extensions.Options.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/Microsoft.Extensions.Primitives.wasm", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.Primitives.wasm", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "WasmResource", "AssetTraitValue": "runtime", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/webcil/Microsoft.Extensions.Primitives.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/Microsoft.JSInterop.wasm", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/Microsoft.JSInterop.wasm", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "WasmResource", "AssetTraitValue": "runtime", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/webcil/Microsoft.JSInterop.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/Microsoft.JSInterop.WebAssembly.wasm", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/Microsoft.JSInterop.WebAssembly.wasm", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "WasmResource", "AssetTraitValue": "runtime", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/webcil/Microsoft.JSInterop.WebAssembly.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/Microsoft.VisualBasic.Core.wasm", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/Microsoft.VisualBasic.Core.wasm", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "WasmResource", "AssetTraitValue": "runtime", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/webcil/Microsoft.VisualBasic.Core.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/Microsoft.VisualBasic.wasm", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/Microsoft.VisualBasic.wasm", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "WasmResource", "AssetTraitValue": "runtime", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/webcil/Microsoft.VisualBasic.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/Microsoft.Win32.Primitives.wasm", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/Microsoft.Win32.Primitives.wasm", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "WasmResource", "AssetTraitValue": "runtime", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/webcil/Microsoft.Win32.Primitives.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/Microsoft.Win32.Registry.wasm", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/Microsoft.Win32.Registry.wasm", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "WasmResource", "AssetTraitValue": "runtime", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/webcil/Microsoft.Win32.Registry.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/mscorlib.wasm", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/mscorlib.wasm", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "WasmResource", "AssetTraitValue": "runtime", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/webcil/mscorlib.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/netstandard.wasm", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/netstandard.wasm", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "WasmResource", "AssetTraitValue": "runtime", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/webcil/netstandard.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.AppContext.wasm", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.AppContext.wasm", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "WasmResource", "AssetTraitValue": "runtime", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/webcil/System.AppContext.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Buffers.wasm", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Buffers.wasm", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "WasmResource", "AssetTraitValue": "runtime", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/webcil/System.Buffers.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Collections.Concurrent.wasm", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Collections.Concurrent.wasm", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "WasmResource", "AssetTraitValue": "runtime", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/webcil/System.Collections.Concurrent.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Collections.Immutable.wasm", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Collections.Immutable.wasm", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "WasmResource", "AssetTraitValue": "runtime", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/webcil/System.Collections.Immutable.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Collections.NonGeneric.wasm", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Collections.NonGeneric.wasm", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "WasmResource", "AssetTraitValue": "runtime", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/webcil/System.Collections.NonGeneric.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Collections.Specialized.wasm", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Collections.Specialized.wasm", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "WasmResource", "AssetTraitValue": "runtime", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/webcil/System.Collections.Specialized.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Collections.wasm", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Collections.wasm", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "WasmResource", "AssetTraitValue": "runtime", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/webcil/System.Collections.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.ComponentModel.Annotations.wasm", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.ComponentModel.Annotations.wasm", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "WasmResource", "AssetTraitValue": "runtime", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/webcil/System.ComponentModel.Annotations.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.ComponentModel.DataAnnotations.wasm", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.ComponentModel.DataAnnotations.wasm", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "WasmResource", "AssetTraitValue": "runtime", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/webcil/System.ComponentModel.DataAnnotations.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.ComponentModel.EventBasedAsync.wasm", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.ComponentModel.EventBasedAsync.wasm", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "WasmResource", "AssetTraitValue": "runtime", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/webcil/System.ComponentModel.EventBasedAsync.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.ComponentModel.Primitives.wasm", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.ComponentModel.Primitives.wasm", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "WasmResource", "AssetTraitValue": "runtime", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/webcil/System.ComponentModel.Primitives.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.ComponentModel.TypeConverter.wasm", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.ComponentModel.TypeConverter.wasm", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "WasmResource", "AssetTraitValue": "runtime", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/webcil/System.ComponentModel.TypeConverter.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.ComponentModel.wasm", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.ComponentModel.wasm", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "WasmResource", "AssetTraitValue": "runtime", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/webcil/System.ComponentModel.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Configuration.wasm", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Configuration.wasm", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "WasmResource", "AssetTraitValue": "runtime", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/webcil/System.Configuration.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Console.wasm", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Console.wasm", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "WasmResource", "AssetTraitValue": "runtime", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/webcil/System.Console.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Core.wasm", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Core.wasm", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "WasmResource", "AssetTraitValue": "runtime", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/webcil/System.Core.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Data.Common.wasm", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Data.Common.wasm", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "WasmResource", "AssetTraitValue": "runtime", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/webcil/System.Data.Common.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Data.DataSetExtensions.wasm", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Data.DataSetExtensions.wasm", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "WasmResource", "AssetTraitValue": "runtime", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/webcil/System.Data.DataSetExtensions.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Data.wasm", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Data.wasm", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "WasmResource", "AssetTraitValue": "runtime", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/webcil/System.Data.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Diagnostics.Contracts.wasm", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Diagnostics.Contracts.wasm", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "WasmResource", "AssetTraitValue": "runtime", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/webcil/System.Diagnostics.Contracts.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Diagnostics.Debug.wasm", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Diagnostics.Debug.wasm", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "WasmResource", "AssetTraitValue": "runtime", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/webcil/System.Diagnostics.Debug.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Diagnostics.DiagnosticSource.wasm", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Diagnostics.DiagnosticSource.wasm", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "WasmResource", "AssetTraitValue": "runtime", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/webcil/System.Diagnostics.DiagnosticSource.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Diagnostics.FileVersionInfo.wasm", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Diagnostics.FileVersionInfo.wasm", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "WasmResource", "AssetTraitValue": "runtime", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/webcil/System.Diagnostics.FileVersionInfo.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Diagnostics.Process.wasm", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Diagnostics.Process.wasm", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "WasmResource", "AssetTraitValue": "runtime", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/webcil/System.Diagnostics.Process.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Diagnostics.StackTrace.wasm", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Diagnostics.StackTrace.wasm", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "WasmResource", "AssetTraitValue": "runtime", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/webcil/System.Diagnostics.StackTrace.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Diagnostics.TextWriterTraceListener.wasm", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Diagnostics.TextWriterTraceListener.wasm", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "WasmResource", "AssetTraitValue": "runtime", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/webcil/System.Diagnostics.TextWriterTraceListener.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Diagnostics.Tools.wasm", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Diagnostics.Tools.wasm", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "WasmResource", "AssetTraitValue": "runtime", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/webcil/System.Diagnostics.Tools.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Diagnostics.TraceSource.wasm", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Diagnostics.TraceSource.wasm", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "WasmResource", "AssetTraitValue": "runtime", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/webcil/System.Diagnostics.TraceSource.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Diagnostics.Tracing.wasm", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Diagnostics.Tracing.wasm", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "WasmResource", "AssetTraitValue": "runtime", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/webcil/System.Diagnostics.Tracing.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Drawing.Primitives.wasm", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Drawing.Primitives.wasm", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "WasmResource", "AssetTraitValue": "runtime", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/webcil/System.Drawing.Primitives.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Drawing.wasm", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Drawing.wasm", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "WasmResource", "AssetTraitValue": "runtime", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/webcil/System.Drawing.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Dynamic.Runtime.wasm", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Dynamic.Runtime.wasm", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "WasmResource", "AssetTraitValue": "runtime", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/webcil/System.Dynamic.Runtime.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Formats.Asn1.wasm", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Formats.Asn1.wasm", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "WasmResource", "AssetTraitValue": "runtime", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/webcil/System.Formats.Asn1.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Formats.Tar.wasm", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Formats.Tar.wasm", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "WasmResource", "AssetTraitValue": "runtime", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/webcil/System.Formats.Tar.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Globalization.Calendars.wasm", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Globalization.Calendars.wasm", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "WasmResource", "AssetTraitValue": "runtime", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/webcil/System.Globalization.Calendars.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Globalization.Extensions.wasm", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Globalization.Extensions.wasm", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "WasmResource", "AssetTraitValue": "runtime", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/webcil/System.Globalization.Extensions.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Globalization.wasm", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Globalization.wasm", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "WasmResource", "AssetTraitValue": "runtime", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/webcil/System.Globalization.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.IO.Compression.Brotli.wasm", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.IO.Compression.Brotli.wasm", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "WasmResource", "AssetTraitValue": "runtime", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/webcil/System.IO.Compression.Brotli.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.IO.Compression.FileSystem.wasm", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.IO.Compression.FileSystem.wasm", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "WasmResource", "AssetTraitValue": "runtime", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/webcil/System.IO.Compression.FileSystem.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.IO.Compression.wasm", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.IO.Compression.wasm", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "WasmResource", "AssetTraitValue": "runtime", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/webcil/System.IO.Compression.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.IO.Compression.ZipFile.wasm", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.IO.Compression.ZipFile.wasm", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "WasmResource", "AssetTraitValue": "runtime", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/webcil/System.IO.Compression.ZipFile.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.IO.FileSystem.AccessControl.wasm", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.IO.FileSystem.AccessControl.wasm", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "WasmResource", "AssetTraitValue": "runtime", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/webcil/System.IO.FileSystem.AccessControl.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.IO.FileSystem.DriveInfo.wasm", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.IO.FileSystem.DriveInfo.wasm", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "WasmResource", "AssetTraitValue": "runtime", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/webcil/System.IO.FileSystem.DriveInfo.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.IO.FileSystem.Primitives.wasm", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.IO.FileSystem.Primitives.wasm", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "WasmResource", "AssetTraitValue": "runtime", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/webcil/System.IO.FileSystem.Primitives.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.IO.FileSystem.wasm", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.IO.FileSystem.wasm", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "WasmResource", "AssetTraitValue": "runtime", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/webcil/System.IO.FileSystem.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.IO.FileSystem.Watcher.wasm", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.IO.FileSystem.Watcher.wasm", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "WasmResource", "AssetTraitValue": "runtime", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/webcil/System.IO.FileSystem.Watcher.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.IO.IsolatedStorage.wasm", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.IO.IsolatedStorage.wasm", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "WasmResource", "AssetTraitValue": "runtime", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/webcil/System.IO.IsolatedStorage.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.IO.MemoryMappedFiles.wasm", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.IO.MemoryMappedFiles.wasm", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "WasmResource", "AssetTraitValue": "runtime", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/webcil/System.IO.MemoryMappedFiles.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.IO.Pipelines.wasm", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.IO.Pipelines.wasm", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "WasmResource", "AssetTraitValue": "runtime", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/webcil/System.IO.Pipelines.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.IO.Pipes.AccessControl.wasm", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.IO.Pipes.AccessControl.wasm", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "WasmResource", "AssetTraitValue": "runtime", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/webcil/System.IO.Pipes.AccessControl.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.IO.Pipes.wasm", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.IO.Pipes.wasm", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "WasmResource", "AssetTraitValue": "runtime", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/webcil/System.IO.Pipes.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.IO.UnmanagedMemoryStream.wasm", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.IO.UnmanagedMemoryStream.wasm", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "WasmResource", "AssetTraitValue": "runtime", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/webcil/System.IO.UnmanagedMemoryStream.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.IO.wasm", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.IO.wasm", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "WasmResource", "AssetTraitValue": "runtime", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/webcil/System.IO.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Linq.Expressions.wasm", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Linq.Expressions.wasm", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "WasmResource", "AssetTraitValue": "runtime", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/webcil/System.Linq.Expressions.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Linq.Parallel.wasm", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Linq.Parallel.wasm", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "WasmResource", "AssetTraitValue": "runtime", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/webcil/System.Linq.Parallel.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Linq.Queryable.wasm", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Linq.Queryable.wasm", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "WasmResource", "AssetTraitValue": "runtime", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/webcil/System.Linq.Queryable.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Linq.wasm", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Linq.wasm", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "WasmResource", "AssetTraitValue": "runtime", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/webcil/System.Linq.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Memory.wasm", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Memory.wasm", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "WasmResource", "AssetTraitValue": "runtime", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/webcil/System.Memory.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Net.Http.Json.wasm", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Net.Http.Json.wasm", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "WasmResource", "AssetTraitValue": "runtime", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/webcil/System.Net.Http.Json.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Net.Http.wasm", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Net.Http.wasm", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "WasmResource", "AssetTraitValue": "runtime", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/webcil/System.Net.Http.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Net.HttpListener.wasm", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Net.HttpListener.wasm", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "WasmResource", "AssetTraitValue": "runtime", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/webcil/System.Net.HttpListener.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Net.Mail.wasm", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Net.Mail.wasm", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "WasmResource", "AssetTraitValue": "runtime", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/webcil/System.Net.Mail.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Net.NameResolution.wasm", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Net.NameResolution.wasm", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "WasmResource", "AssetTraitValue": "runtime", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/webcil/System.Net.NameResolution.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Net.NetworkInformation.wasm", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Net.NetworkInformation.wasm", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "WasmResource", "AssetTraitValue": "runtime", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/webcil/System.Net.NetworkInformation.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Net.Ping.wasm", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Net.Ping.wasm", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "WasmResource", "AssetTraitValue": "runtime", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/webcil/System.Net.Ping.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Net.Primitives.wasm", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Net.Primitives.wasm", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "WasmResource", "AssetTraitValue": "runtime", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/webcil/System.Net.Primitives.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Net.Quic.wasm", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Net.Quic.wasm", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "WasmResource", "AssetTraitValue": "runtime", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/webcil/System.Net.Quic.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Net.Requests.wasm", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Net.Requests.wasm", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "WasmResource", "AssetTraitValue": "runtime", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/webcil/System.Net.Requests.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Net.Security.wasm", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Net.Security.wasm", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "WasmResource", "AssetTraitValue": "runtime", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/webcil/System.Net.Security.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Net.ServicePoint.wasm", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Net.ServicePoint.wasm", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "WasmResource", "AssetTraitValue": "runtime", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/webcil/System.Net.ServicePoint.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Net.Sockets.wasm", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Net.Sockets.wasm", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "WasmResource", "AssetTraitValue": "runtime", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/webcil/System.Net.Sockets.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Net.wasm", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Net.wasm", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "WasmResource", "AssetTraitValue": "runtime", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/webcil/System.Net.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Net.WebClient.wasm", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Net.WebClient.wasm", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "WasmResource", "AssetTraitValue": "runtime", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/webcil/System.Net.WebClient.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Net.WebHeaderCollection.wasm", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Net.WebHeaderCollection.wasm", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "WasmResource", "AssetTraitValue": "runtime", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/webcil/System.Net.WebHeaderCollection.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Net.WebProxy.wasm", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Net.WebProxy.wasm", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "WasmResource", "AssetTraitValue": "runtime", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/webcil/System.Net.WebProxy.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Net.WebSockets.Client.wasm", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Net.WebSockets.Client.wasm", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "WasmResource", "AssetTraitValue": "runtime", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/webcil/System.Net.WebSockets.Client.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Net.WebSockets.wasm", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Net.WebSockets.wasm", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "WasmResource", "AssetTraitValue": "runtime", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/webcil/System.Net.WebSockets.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Numerics.Vectors.wasm", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Numerics.Vectors.wasm", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "WasmResource", "AssetTraitValue": "runtime", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/webcil/System.Numerics.Vectors.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Numerics.wasm", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Numerics.wasm", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "WasmResource", "AssetTraitValue": "runtime", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/webcil/System.Numerics.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.ObjectModel.wasm", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.ObjectModel.wasm", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "WasmResource", "AssetTraitValue": "runtime", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/webcil/System.ObjectModel.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Private.CoreLib.wasm", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Private.CoreLib.wasm", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "WasmResource", "AssetTraitValue": "runtime", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/webcil/System.Private.CoreLib.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Private.DataContractSerialization.wasm", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Private.DataContractSerialization.wasm", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "WasmResource", "AssetTraitValue": "runtime", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/webcil/System.Private.DataContractSerialization.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Private.Uri.wasm", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Private.Uri.wasm", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "WasmResource", "AssetTraitValue": "runtime", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/webcil/System.Private.Uri.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Private.Xml.Linq.wasm", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Private.Xml.Linq.wasm", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "WasmResource", "AssetTraitValue": "runtime", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/webcil/System.Private.Xml.Linq.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Private.Xml.wasm", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Private.Xml.wasm", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "WasmResource", "AssetTraitValue": "runtime", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/webcil/System.Private.Xml.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Reflection.DispatchProxy.wasm", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Reflection.DispatchProxy.wasm", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "WasmResource", "AssetTraitValue": "runtime", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/webcil/System.Reflection.DispatchProxy.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Reflection.Emit.ILGeneration.wasm", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Reflection.Emit.ILGeneration.wasm", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "WasmResource", "AssetTraitValue": "runtime", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/webcil/System.Reflection.Emit.ILGeneration.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Reflection.Emit.Lightweight.wasm", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Reflection.Emit.Lightweight.wasm", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "WasmResource", "AssetTraitValue": "runtime", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/webcil/System.Reflection.Emit.Lightweight.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Reflection.Emit.wasm", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Reflection.Emit.wasm", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "WasmResource", "AssetTraitValue": "runtime", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/webcil/System.Reflection.Emit.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Reflection.Extensions.wasm", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Reflection.Extensions.wasm", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "WasmResource", "AssetTraitValue": "runtime", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/webcil/System.Reflection.Extensions.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Reflection.Metadata.wasm", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Reflection.Metadata.wasm", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "WasmResource", "AssetTraitValue": "runtime", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/webcil/System.Reflection.Metadata.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Reflection.Primitives.wasm", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Reflection.Primitives.wasm", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "WasmResource", "AssetTraitValue": "runtime", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/webcil/System.Reflection.Primitives.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Reflection.TypeExtensions.wasm", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Reflection.TypeExtensions.wasm", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "WasmResource", "AssetTraitValue": "runtime", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/webcil/System.Reflection.TypeExtensions.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Reflection.wasm", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Reflection.wasm", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "WasmResource", "AssetTraitValue": "runtime", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/webcil/System.Reflection.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Resources.Reader.wasm", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Resources.Reader.wasm", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "WasmResource", "AssetTraitValue": "runtime", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/webcil/System.Resources.Reader.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Resources.ResourceManager.wasm", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Resources.ResourceManager.wasm", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "WasmResource", "AssetTraitValue": "runtime", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/webcil/System.Resources.ResourceManager.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Resources.Writer.wasm", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Resources.Writer.wasm", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "WasmResource", "AssetTraitValue": "runtime", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/webcil/System.Resources.Writer.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Runtime.CompilerServices.Unsafe.wasm", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Runtime.CompilerServices.Unsafe.wasm", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "WasmResource", "AssetTraitValue": "runtime", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/webcil/System.Runtime.CompilerServices.Unsafe.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Runtime.CompilerServices.VisualC.wasm", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Runtime.CompilerServices.VisualC.wasm", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "WasmResource", "AssetTraitValue": "runtime", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/webcil/System.Runtime.CompilerServices.VisualC.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Runtime.Extensions.wasm", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Runtime.Extensions.wasm", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "WasmResource", "AssetTraitValue": "runtime", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/webcil/System.Runtime.Extensions.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Runtime.Handles.wasm", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Runtime.Handles.wasm", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "WasmResource", "AssetTraitValue": "runtime", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/webcil/System.Runtime.Handles.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Runtime.InteropServices.JavaScript.wasm", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Runtime.InteropServices.JavaScript.wasm", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "WasmResource", "AssetTraitValue": "runtime", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/webcil/System.Runtime.InteropServices.JavaScript.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Runtime.InteropServices.RuntimeInformation.wasm", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Runtime.InteropServices.RuntimeInformation.wasm", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "WasmResource", "AssetTraitValue": "runtime", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/webcil/System.Runtime.InteropServices.RuntimeInformation.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Runtime.InteropServices.wasm", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Runtime.InteropServices.wasm", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "WasmResource", "AssetTraitValue": "runtime", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/webcil/System.Runtime.InteropServices.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Runtime.Intrinsics.wasm", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Runtime.Intrinsics.wasm", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "WasmResource", "AssetTraitValue": "runtime", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/webcil/System.Runtime.Intrinsics.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Runtime.Loader.wasm", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Runtime.Loader.wasm", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "WasmResource", "AssetTraitValue": "runtime", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/webcil/System.Runtime.Loader.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Runtime.Numerics.wasm", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Runtime.Numerics.wasm", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "WasmResource", "AssetTraitValue": "runtime", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/webcil/System.Runtime.Numerics.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Runtime.Serialization.Formatters.wasm", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Runtime.Serialization.Formatters.wasm", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "WasmResource", "AssetTraitValue": "runtime", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/webcil/System.Runtime.Serialization.Formatters.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Runtime.Serialization.Json.wasm", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Runtime.Serialization.Json.wasm", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "WasmResource", "AssetTraitValue": "runtime", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/webcil/System.Runtime.Serialization.Json.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Runtime.Serialization.Primitives.wasm", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Runtime.Serialization.Primitives.wasm", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "WasmResource", "AssetTraitValue": "runtime", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/webcil/System.Runtime.Serialization.Primitives.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Runtime.Serialization.wasm", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Runtime.Serialization.wasm", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "WasmResource", "AssetTraitValue": "runtime", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/webcil/System.Runtime.Serialization.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Runtime.Serialization.Xml.wasm", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Runtime.Serialization.Xml.wasm", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "WasmResource", "AssetTraitValue": "runtime", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/webcil/System.Runtime.Serialization.Xml.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Runtime.wasm", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Runtime.wasm", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "WasmResource", "AssetTraitValue": "runtime", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/webcil/System.Runtime.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Security.AccessControl.wasm", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Security.AccessControl.wasm", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "WasmResource", "AssetTraitValue": "runtime", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/webcil/System.Security.AccessControl.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Security.Claims.wasm", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Security.Claims.wasm", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "WasmResource", "AssetTraitValue": "runtime", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/webcil/System.Security.Claims.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Security.Cryptography.Algorithms.wasm", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Security.Cryptography.Algorithms.wasm", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "WasmResource", "AssetTraitValue": "runtime", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/webcil/System.Security.Cryptography.Algorithms.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Security.Cryptography.Cng.wasm", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Security.Cryptography.Cng.wasm", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "WasmResource", "AssetTraitValue": "runtime", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/webcil/System.Security.Cryptography.Cng.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Security.Cryptography.Csp.wasm", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Security.Cryptography.Csp.wasm", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "WasmResource", "AssetTraitValue": "runtime", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/webcil/System.Security.Cryptography.Csp.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Security.Cryptography.Encoding.wasm", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Security.Cryptography.Encoding.wasm", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "WasmResource", "AssetTraitValue": "runtime", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/webcil/System.Security.Cryptography.Encoding.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Security.Cryptography.OpenSsl.wasm", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Security.Cryptography.OpenSsl.wasm", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "WasmResource", "AssetTraitValue": "runtime", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/webcil/System.Security.Cryptography.OpenSsl.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Security.Cryptography.Primitives.wasm", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Security.Cryptography.Primitives.wasm", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "WasmResource", "AssetTraitValue": "runtime", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/webcil/System.Security.Cryptography.Primitives.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Security.Cryptography.wasm", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Security.Cryptography.wasm", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "WasmResource", "AssetTraitValue": "runtime", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/webcil/System.Security.Cryptography.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Security.Cryptography.X509Certificates.wasm", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Security.Cryptography.X509Certificates.wasm", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "WasmResource", "AssetTraitValue": "runtime", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/webcil/System.Security.Cryptography.X509Certificates.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Security.Principal.wasm", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Security.Principal.wasm", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "WasmResource", "AssetTraitValue": "runtime", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/webcil/System.Security.Principal.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Security.Principal.Windows.wasm", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Security.Principal.Windows.wasm", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "WasmResource", "AssetTraitValue": "runtime", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/webcil/System.Security.Principal.Windows.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Security.SecureString.wasm", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Security.SecureString.wasm", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "WasmResource", "AssetTraitValue": "runtime", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/webcil/System.Security.SecureString.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Security.wasm", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Security.wasm", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "WasmResource", "AssetTraitValue": "runtime", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/webcil/System.Security.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.ServiceModel.Web.wasm", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.ServiceModel.Web.wasm", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "WasmResource", "AssetTraitValue": "runtime", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/webcil/System.ServiceModel.Web.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.ServiceProcess.wasm", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.ServiceProcess.wasm", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "WasmResource", "AssetTraitValue": "runtime", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/webcil/System.ServiceProcess.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Text.Encoding.CodePages.wasm", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Text.Encoding.CodePages.wasm", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "WasmResource", "AssetTraitValue": "runtime", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/webcil/System.Text.Encoding.CodePages.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Text.Encoding.Extensions.wasm", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Text.Encoding.Extensions.wasm", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "WasmResource", "AssetTraitValue": "runtime", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/webcil/System.Text.Encoding.Extensions.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Text.Encoding.wasm", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Text.Encoding.wasm", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "WasmResource", "AssetTraitValue": "runtime", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/webcil/System.Text.Encoding.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Text.Encodings.Web.wasm", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Text.Encodings.Web.wasm", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "WasmResource", "AssetTraitValue": "runtime", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/webcil/System.Text.Encodings.Web.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Text.Json.wasm", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Text.Json.wasm", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "WasmResource", "AssetTraitValue": "runtime", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/webcil/System.Text.Json.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Text.RegularExpressions.wasm", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Text.RegularExpressions.wasm", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "WasmResource", "AssetTraitValue": "runtime", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/webcil/System.Text.RegularExpressions.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Threading.Channels.wasm", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Threading.Channels.wasm", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "WasmResource", "AssetTraitValue": "runtime", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/webcil/System.Threading.Channels.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Threading.Overlapped.wasm", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Threading.Overlapped.wasm", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "WasmResource", "AssetTraitValue": "runtime", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/webcil/System.Threading.Overlapped.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Threading.Tasks.Dataflow.wasm", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Threading.Tasks.Dataflow.wasm", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "WasmResource", "AssetTraitValue": "runtime", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/webcil/System.Threading.Tasks.Dataflow.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Threading.Tasks.Extensions.wasm", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Threading.Tasks.Extensions.wasm", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "WasmResource", "AssetTraitValue": "runtime", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/webcil/System.Threading.Tasks.Extensions.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Threading.Tasks.Parallel.wasm", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Threading.Tasks.Parallel.wasm", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "WasmResource", "AssetTraitValue": "runtime", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/webcil/System.Threading.Tasks.Parallel.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Threading.Tasks.wasm", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Threading.Tasks.wasm", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "WasmResource", "AssetTraitValue": "runtime", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/webcil/System.Threading.Tasks.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Threading.Thread.wasm", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Threading.Thread.wasm", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "WasmResource", "AssetTraitValue": "runtime", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/webcil/System.Threading.Thread.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Threading.ThreadPool.wasm", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Threading.ThreadPool.wasm", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "WasmResource", "AssetTraitValue": "runtime", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/webcil/System.Threading.ThreadPool.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Threading.Timer.wasm", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Threading.Timer.wasm", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "WasmResource", "AssetTraitValue": "runtime", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/webcil/System.Threading.Timer.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Threading.wasm", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Threading.wasm", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "WasmResource", "AssetTraitValue": "runtime", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/webcil/System.Threading.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Transactions.Local.wasm", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Transactions.Local.wasm", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "WasmResource", "AssetTraitValue": "runtime", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/webcil/System.Transactions.Local.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Transactions.wasm", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Transactions.wasm", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "WasmResource", "AssetTraitValue": "runtime", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/webcil/System.Transactions.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.ValueTuple.wasm", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.ValueTuple.wasm", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "WasmResource", "AssetTraitValue": "runtime", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/webcil/System.ValueTuple.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.wasm", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.wasm", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "WasmResource", "AssetTraitValue": "runtime", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/webcil/System.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Web.HttpUtility.wasm", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Web.HttpUtility.wasm", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "WasmResource", "AssetTraitValue": "runtime", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/webcil/System.Web.HttpUtility.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Web.wasm", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Web.wasm", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "WasmResource", "AssetTraitValue": "runtime", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/webcil/System.Web.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Windows.wasm", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Windows.wasm", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "WasmResource", "AssetTraitValue": "runtime", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/webcil/System.Windows.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Xml.Linq.wasm", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Xml.Linq.wasm", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "WasmResource", "AssetTraitValue": "runtime", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/webcil/System.Xml.Linq.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Xml.ReaderWriter.wasm", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Xml.ReaderWriter.wasm", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "WasmResource", "AssetTraitValue": "runtime", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/webcil/System.Xml.ReaderWriter.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Xml.Serialization.wasm", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Xml.Serialization.wasm", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "WasmResource", "AssetTraitValue": "runtime", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/webcil/System.Xml.Serialization.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Xml.wasm", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Xml.wasm", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "WasmResource", "AssetTraitValue": "runtime", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/webcil/System.Xml.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Xml.XDocument.wasm", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Xml.XDocument.wasm", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "WasmResource", "AssetTraitValue": "runtime", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/webcil/System.Xml.XDocument.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Xml.XmlDocument.wasm", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Xml.XmlDocument.wasm", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "WasmResource", "AssetTraitValue": "runtime", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/webcil/System.Xml.XmlDocument.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Xml.XmlSerializer.wasm", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Xml.XmlSerializer.wasm", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "WasmResource", "AssetTraitValue": "runtime", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/webcil/System.Xml.XmlSerializer.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Xml.XPath.wasm", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Xml.XPath.wasm", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "WasmResource", "AssetTraitValue": "runtime", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/webcil/System.Xml.XPath.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Xml.XPath.XDocument.wasm", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Xml.XPath.XDocument.wasm", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "WasmResource", "AssetTraitValue": "runtime", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/webcil/System.Xml.XPath.XDocument.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/UserManagement.Client.pdb", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/UserManagement.Client.pdb", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "WasmResource", "AssetTraitValue": "symbol", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "obj/Debug/net8.0/UserManagement.Client.pdb"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/UserManagement.Client.wasm", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/UserManagement.Client.wasm", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "WasmResource", "AssetTraitValue": "runtime", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/webcil/UserManagement.Client.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/UserManagement.Shared.pdb", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/UserManagement.Shared.pdb", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "WasmResource", "AssetTraitValue": "symbol", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Shared/UserManagement.Shared/bin/Debug/net8.0/UserManagement.Shared.pdb"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/UserManagement.Shared.wasm", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/UserManagement.Shared.wasm", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "WasmResource", "AssetTraitValue": "runtime", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/webcil/UserManagement.Shared.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/WindowsBase.wasm", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/WindowsBase.wasm", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "WasmResource", "AssetTraitValue": "runtime", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/webcil/WindowsBase.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/compressed/07ffolibyc.gz", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Security.Claims.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Security.Claims.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Security.Claims.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/compressed/0acdbd7pxj.gz", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Runtime.CompilerServices.Unsafe.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Runtime.CompilerServices.Unsafe.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Runtime.CompilerServices.Unsafe.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/compressed/0lgp9t0j87.gz", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Diagnostics.Contracts.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Diagnostics.Contracts.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Diagnostics.Contracts.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/compressed/15my4v2ii8.gz", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/Microsoft.Win32.Primitives.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/Microsoft.Win32.Primitives.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/Microsoft.Win32.Primitives.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/compressed/270othyzly.gz", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/dotnet.runtime.js.map.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/dotnet.runtime.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/dotnet.runtime.js.map"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/compressed/28aljlsnxw.gz", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.IO.Compression.FileSystem.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.IO.Compression.FileSystem.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.IO.Compression.FileSystem.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/compressed/2a9wcm2hsn.gz", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.IO.Compression.ZipFile.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.IO.Compression.ZipFile.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.IO.Compression.ZipFile.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/compressed/2fks21rc06.gz", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Security.Cryptography.Cng.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Security.Cryptography.Cng.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Security.Cryptography.Cng.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/compressed/2gvm16m2j9.gz", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Windows.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Windows.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Windows.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/compressed/2irh6q7uxw.gz", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Diagnostics.StackTrace.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Diagnostics.StackTrace.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Diagnostics.StackTrace.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/compressed/35w1sfy8oe.gz", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/Microsoft.AspNetCore.Components.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/Microsoft.AspNetCore.Components.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/Microsoft.AspNetCore.Components.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/compressed/36dbu14fk3.gz", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Threading.ThreadPool.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Threading.ThreadPool.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Threading.ThreadPool.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/compressed/3n2xa21aa7.gz", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.IO.IsolatedStorage.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.IO.IsolatedStorage.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.IO.IsolatedStorage.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/compressed/40myxerc5k.gz", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.ObjectModel.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.ObjectModel.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.ObjectModel.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/compressed/41e6pwjnpw.gz", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Security.Cryptography.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Security.Cryptography.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Security.Cryptography.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/compressed/4lx5jycmz3.gz", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Runtime.CompilerServices.VisualC.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Runtime.CompilerServices.VisualC.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Runtime.CompilerServices.VisualC.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/compressed/4na471kjeo.gz", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/dotnet.js.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/dotnet.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/dotnet.js"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/compressed/4nfou5v55v.gz", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Runtime.Serialization.Xml.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Runtime.Serialization.Xml.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Runtime.Serialization.Xml.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/compressed/4quggyoc78.gz", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/Microsoft.CSharp.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/Microsoft.CSharp.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/Microsoft.CSharp.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/compressed/4sol7ydate.gz", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/UserManagement.Shared.pdb.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/UserManagement.Shared.pdb", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/UserManagement.Shared.pdb"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/compressed/4vtt3v3svj.gz", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Runtime.Handles.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Runtime.Handles.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Runtime.Handles.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/compressed/4wxm372u72.gz", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Resources.ResourceManager.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Resources.ResourceManager.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Resources.ResourceManager.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/compressed/4x1umxsgyu.gz", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Private.Uri.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Private.Uri.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Private.Uri.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/compressed/5f416tlhx9.gz", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.FileProviders.Abstractions.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/Microsoft.Extensions.FileProviders.Abstractions.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/Microsoft.Extensions.FileProviders.Abstractions.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/compressed/5hf2c4adlz.gz", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.Logging.Abstractions.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/Microsoft.Extensions.Logging.Abstractions.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/Microsoft.Extensions.Logging.Abstractions.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/compressed/5ihunpaple.gz", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Security.AccessControl.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Security.AccessControl.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Security.AccessControl.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/compressed/5mc763xnu8.gz", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Text.Json.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Text.Json.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Text.Json.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/compressed/5qo4nxs9vt.gz", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Core.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Core.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Core.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/compressed/5rkqccpc1j.gz", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Web.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Web.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Web.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/compressed/5rz5ipel43.gz", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Net.Mail.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Net.Mail.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Net.Mail.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/compressed/6drysw8arx.gz", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.ServiceProcess.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.ServiceProcess.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.ServiceProcess.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/compressed/6i20g6cvhc.gz", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Formats.Asn1.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Formats.Asn1.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Formats.Asn1.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/compressed/6tg7f2p5oo.gz", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Text.Encoding.Extensions.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Text.Encoding.Extensions.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Text.Encoding.Extensions.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/compressed/6zq8ikbr7h.gz", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Diagnostics.FileVersionInfo.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Diagnostics.FileVersionInfo.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Diagnostics.FileVersionInfo.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/compressed/6zunw012or.gz", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Net.Primitives.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Net.Primitives.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Net.Primitives.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/compressed/72n9tv6e6p.gz", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Net.Requests.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Net.Requests.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Net.Requests.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/compressed/78vyd11dwq.gz", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.IO.FileSystem.AccessControl.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.IO.FileSystem.AccessControl.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.IO.FileSystem.AccessControl.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/compressed/7arculwhxy.gz", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.ComponentModel.Primitives.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.ComponentModel.Primitives.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.ComponentModel.Primitives.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/compressed/7gb0ec2i0x.gz", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/dotnet.js.map.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/dotnet.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/dotnet.js.map"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/compressed/83ljiwwb99.gz", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Net.Http.Json.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Net.Http.Json.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Net.Http.Json.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/compressed/854yfk224j.gz", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Buffers.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Buffers.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Buffers.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/compressed/85dduijwrl.gz", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Net.Security.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Net.Security.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Net.Security.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/compressed/8916p5h4b5.gz", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/netstandard.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/netstandard.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/netstandard.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/compressed/8fg18ozzlu.gz", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Xml.XDocument.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Xml.XDocument.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Xml.XDocument.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/compressed/8hhs0o14xq.gz", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/Microsoft.AspNetCore.Authorization.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/Microsoft.AspNetCore.Authorization.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/Microsoft.AspNetCore.Authorization.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/compressed/8jp08z2wkl.gz", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Runtime.Intrinsics.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Runtime.Intrinsics.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Runtime.Intrinsics.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/compressed/8n4031df9o.gz", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Threading.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Threading.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Threading.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/compressed/8rynixsyrn.gz", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/UserManagement.Shared.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/UserManagement.Shared.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/UserManagement.Shared.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/compressed/8vawqh70io.gz", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Web.HttpUtility.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Web.HttpUtility.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Web.HttpUtility.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/compressed/8ymzksiby0.gz", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.Options.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/Microsoft.Extensions.Options.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/Microsoft.Extensions.Options.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/compressed/8zs74n3dw0.gz", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Threading.Overlapped.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Threading.Overlapped.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Threading.Overlapped.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/compressed/98xxeqcul6.gz", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Runtime.Extensions.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Runtime.Extensions.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Runtime.Extensions.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/compressed/9mokglfe83.gz", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.ComponentModel.DataAnnotations.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.ComponentModel.DataAnnotations.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.ComponentModel.DataAnnotations.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/compressed/9p0w2giv0t.gz", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Reflection.TypeExtensions.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Reflection.TypeExtensions.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Reflection.TypeExtensions.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/compressed/9qio5a4l2p.gz", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Security.Cryptography.Encoding.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Security.Cryptography.Encoding.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Security.Cryptography.Encoding.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/compressed/9uow0mfoo0.gz", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Globalization.Calendars.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Globalization.Calendars.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Globalization.Calendars.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/compressed/9xi8hdl6gr.gz", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Private.DataContractSerialization.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Private.DataContractSerialization.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Private.DataContractSerialization.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/compressed/9yfhqwv6op.gz", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.Configuration.FileExtensions.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/Microsoft.Extensions.Configuration.FileExtensions.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/Microsoft.Extensions.Configuration.FileExtensions.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/compressed/ahoao1std5.gz", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.IO.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.IO.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.IO.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/compressed/asryhxw8qc.gz", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Resources.Reader.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Resources.Reader.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Resources.Reader.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/compressed/awvvng3kwh.gz", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Diagnostics.Debug.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Diagnostics.Debug.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Diagnostics.Debug.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/compressed/axccuhexri.gz", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Xml.XmlDocument.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Xml.XmlDocument.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Xml.XmlDocument.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/compressed/ayaodefgk5.gz", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Reflection.Emit.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Reflection.Emit.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Reflection.Emit.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/compressed/azhtdpuy1t.gz", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Net.ServicePoint.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Net.ServicePoint.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Net.ServicePoint.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/compressed/b7jqfi4hc5.gz", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Runtime.Serialization.Primitives.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Runtime.Serialization.Primitives.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Runtime.Serialization.Primitives.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/compressed/ba0ombotpr.gz", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Xml.XPath.XDocument.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Xml.XPath.XDocument.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Xml.XPath.XDocument.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/compressed/beapsu0juy.gz", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Reflection.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Reflection.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Reflection.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/compressed/bg3cbp78bd.gz", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Net.Quic.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Net.Quic.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Net.Quic.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/compressed/bw2axesllz.gz", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Security.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Security.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Security.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/compressed/bwzq65pj7x.gz", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/icudt_EFIGS.dat.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/icudt_EFIGS.dat", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/icudt_EFIGS.dat"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/compressed/bxahfcvsdb.gz", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/WindowsBase.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/WindowsBase.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/WindowsBase.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/compressed/c2a9zugwru.gz", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.IO.MemoryMappedFiles.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.IO.MemoryMappedFiles.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.IO.MemoryMappedFiles.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/compressed/cb3se1yrkd.gz", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.ValueTuple.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.ValueTuple.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.ValueTuple.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/compressed/ckq61c7gf1.gz", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.Configuration.Abstractions.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/Microsoft.Extensions.Configuration.Abstractions.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/Microsoft.Extensions.Configuration.Abstractions.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/compressed/d76o97gli8.gz", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Data.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Data.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Data.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/compressed/d7ao2r5mc4.gz", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.IO.Compression.Brotli.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.IO.Compression.Brotli.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.IO.Compression.Brotli.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/compressed/dm0vtgtf3u.gz", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/UserManagement.Client.pdb.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/UserManagement.Client.pdb", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/UserManagement.Client.pdb"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/compressed/dzlvdsv4a3.gz", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.IO.FileSystem.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.IO.FileSystem.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.IO.FileSystem.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/compressed/e0nwmtmzlm.gz", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.Logging.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/Microsoft.Extensions.Logging.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/Microsoft.Extensions.Logging.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/compressed/e5r0ffly4h.gz", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Numerics.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Numerics.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Numerics.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/compressed/eacirbo3hs.gz", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.FileProviders.Physical.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/Microsoft.Extensions.FileProviders.Physical.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/Microsoft.Extensions.FileProviders.Physical.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/compressed/eez5008yfy.gz", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Net.NetworkInformation.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Net.NetworkInformation.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Net.NetworkInformation.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/compressed/ehvzq5ytxm.gz", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.ServiceModel.Web.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.ServiceModel.Web.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.ServiceModel.Web.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/compressed/eidq343vqk.gz", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/icudt_CJK.dat.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/icudt_CJK.dat", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/icudt_CJK.dat"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/compressed/eu9hof3xar.gz", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Runtime.Serialization.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Runtime.Serialization.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Runtime.Serialization.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/compressed/f1bpiqm2py.gz", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Security.Cryptography.X509Certificates.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Security.Cryptography.X509Certificates.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Security.Cryptography.X509Certificates.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/compressed/fi9mi9qvbk.gz", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.FileSystemGlobbing.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/Microsoft.Extensions.FileSystemGlobbing.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/Microsoft.Extensions.FileSystemGlobbing.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/compressed/fphyodujqg.gz", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Net.WebProxy.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Net.WebProxy.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Net.WebProxy.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/compressed/fqtqbt06h5.gz", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Diagnostics.Process.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Diagnostics.Process.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Diagnostics.Process.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/compressed/frvb3disbh.gz", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/dotnet.native.8.0.20.frvb3disbh.js.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/dotnet.native.8.0.20.frvb3disbh.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/dotnet.native.8.0.20.frvb3disbh.js"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/compressed/fu8tlaa4ai.gz", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Memory.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Memory.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Memory.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/compressed/fw10ya8xyj.gz", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.Primitives.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/Microsoft.Extensions.Primitives.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/Microsoft.Extensions.Primitives.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/compressed/g5nw2slk8a.gz", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.IO.Compression.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.IO.Compression.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.IO.Compression.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/compressed/gped7iqcy4.gz", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/dotnet.runtime.8.0.20.gped7iqcy4.js.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/dotnet.runtime.8.0.20.gped7iqcy4.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/dotnet.runtime.8.0.20.gped7iqcy4.js"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/compressed/gxchvkke49.gz", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.ComponentModel.Annotations.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.ComponentModel.Annotations.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.ComponentModel.Annotations.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/compressed/h20d6vyg76.gz", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Runtime.InteropServices.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Runtime.InteropServices.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Runtime.InteropServices.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/compressed/h485x936yv.gz", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.IO.FileSystem.Watcher.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.IO.FileSystem.Watcher.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.IO.FileSystem.Watcher.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/compressed/h4id1p8tdd.gz", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Private.Xml.Linq.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Private.Xml.Linq.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Private.Xml.Linq.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/compressed/h9r9v0s22k.gz", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.ComponentModel.EventBasedAsync.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.ComponentModel.EventBasedAsync.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.ComponentModel.EventBasedAsync.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/compressed/hoa8f8zmgv.gz", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/Microsoft.JSInterop.WebAssembly.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/Microsoft.JSInterop.WebAssembly.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/Microsoft.JSInterop.WebAssembly.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/compressed/htq8ejcrz0.gz", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Threading.Timer.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Threading.Timer.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Threading.Timer.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/compressed/i1nweadkwg.gz", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Runtime.Serialization.Json.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Runtime.Serialization.Json.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Runtime.Serialization.Json.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/compressed/i865ca7c6m.gz", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Threading.Tasks.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Threading.Tasks.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Threading.Tasks.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/compressed/ibjz4coesv.gz", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Security.Cryptography.Algorithms.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Security.Cryptography.Algorithms.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Security.Cryptography.Algorithms.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/compressed/im9k3bf1oi.gz", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.ComponentModel.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.ComponentModel.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.ComponentModel.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/compressed/irqw7lujpm.gz", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.IO.FileSystem.Primitives.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.IO.FileSystem.Primitives.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.IO.FileSystem.Primitives.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/compressed/iwctelmhw2.gz", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.IO.Pipes.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.IO.Pipes.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.IO.Pipes.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/compressed/izt3eb99eo.gz", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Collections.NonGeneric.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Collections.NonGeneric.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Collections.NonGeneric.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/compressed/j405sphvmt.gz", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Dynamic.Runtime.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Dynamic.Runtime.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Dynamic.Runtime.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/compressed/jhswk11nvh.gz", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Linq.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Linq.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Linq.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/compressed/jtqs16l9kl.gz", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Net.HttpListener.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Net.HttpListener.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Net.HttpListener.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/compressed/jwq1yojdtb.gz", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/icudt_no_CJK.dat.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/icudt_no_CJK.dat", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/icudt_no_CJK.dat"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/compressed/jzjxe5mgh8.gz", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Text.Encoding.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Text.Encoding.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Text.Encoding.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/compressed/k3sudb2y2d.gz", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Net.WebClient.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Net.WebClient.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Net.WebClient.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/compressed/k8ojzrow9p.gz", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Net.Ping.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Net.Ping.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Net.Ping.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/compressed/kd0n71k0t8.gz", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Text.Encodings.Web.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Text.Encodings.Web.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Text.Encodings.Web.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/compressed/kfqmjaqed6.gz", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Security.Cryptography.Csp.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Security.Cryptography.Csp.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Security.Cryptography.Csp.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/compressed/kg1avfuldk.gz", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Xml.XmlSerializer.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Xml.XmlSerializer.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Xml.XmlSerializer.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/compressed/ktq5ehldc0.gz", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/Microsoft.AspNetCore.Components.Web.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/Microsoft.AspNetCore.Components.Web.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/Microsoft.AspNetCore.Components.Web.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/compressed/l1khp64u87.gz", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Diagnostics.TraceSource.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Diagnostics.TraceSource.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Diagnostics.TraceSource.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/compressed/l5f0cp357f.gz", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/Microsoft.VisualBasic.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/Microsoft.VisualBasic.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/Microsoft.VisualBasic.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/compressed/le1olr5xag.gz", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/dotnet.native.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/dotnet.native.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/dotnet.native.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/compressed/lioznsrwy4.gz", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Console.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Console.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Console.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/compressed/lqunef7qcr.gz", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/Microsoft.AspNetCore.Components.Forms.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/Microsoft.AspNetCore.Components.Forms.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/Microsoft.AspNetCore.Components.Forms.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/compressed/lup115zvdz.gz", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Drawing.Primitives.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Drawing.Primitives.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Drawing.Primitives.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/compressed/m3ryu2uui2.gz", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Numerics.Vectors.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Numerics.Vectors.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Numerics.Vectors.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/compressed/m9lg4zavti.gz", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Diagnostics.TextWriterTraceListener.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Diagnostics.TextWriterTraceListener.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Diagnostics.TextWriterTraceListener.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/compressed/m9w81nn5bw.gz", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/Microsoft.Win32.Registry.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/Microsoft.Win32.Registry.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/Microsoft.Win32.Registry.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/compressed/mgzbh8s5oz.gz", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Collections.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Collections.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Collections.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/compressed/mh6vcvd7vn.gz", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/blazor.webassembly.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/blazor.webassembly.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/blazor.webassembly.js"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/compressed/mk07uqclsd.gz", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Runtime.Numerics.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Runtime.Numerics.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Runtime.Numerics.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/compressed/mk1382fgdj.gz", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Threading.Tasks.Extensions.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Threading.Tasks.Extensions.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Threading.Tasks.Extensions.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/compressed/mo2jrlmbug.gz", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Runtime.InteropServices.JavaScript.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Runtime.InteropServices.JavaScript.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Runtime.InteropServices.JavaScript.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/compressed/mq4ukaau05.gz", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.DependencyInjection.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/Microsoft.Extensions.DependencyInjection.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/Microsoft.Extensions.DependencyInjection.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/compressed/mqlyn7ro20.gz", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Linq.Queryable.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Linq.Queryable.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Linq.Queryable.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/compressed/n9oeq8jf0w.gz", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Runtime.Loader.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Runtime.Loader.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Runtime.Loader.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/compressed/nbnclzr1cg.gz", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.ComponentModel.TypeConverter.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.ComponentModel.TypeConverter.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.ComponentModel.TypeConverter.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/compressed/ncilf6yj1k.gz", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Private.Xml.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Private.Xml.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Private.Xml.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/compressed/nlbdgpa6og.gz", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Transactions.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Transactions.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Transactions.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/compressed/nqiwvxn61d.gz", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.IO.UnmanagedMemoryStream.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.IO.UnmanagedMemoryStream.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.IO.UnmanagedMemoryStream.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/compressed/nwf5eg222x.gz", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Collections.Concurrent.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Collections.Concurrent.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Collections.Concurrent.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/compressed/ny4e5z4nod.gz", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Net.WebSockets.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Net.WebSockets.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Net.WebSockets.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/compressed/o3yxul1csd.gz", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Threading.Thread.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Threading.Thread.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Threading.Thread.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/compressed/onhwusbwps.gz", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Threading.Tasks.Dataflow.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Threading.Tasks.Dataflow.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Threading.Tasks.Dataflow.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/compressed/p2q3jca13v.gz", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/compressed/p4aozwvi9i.gz", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/mscorlib.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/mscorlib.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/mscorlib.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/compressed/pi3r1jcfbt.gz", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Text.Encoding.CodePages.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Text.Encoding.CodePages.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Text.Encoding.CodePages.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/compressed/q08v27t7eq.gz", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Text.RegularExpressions.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Text.RegularExpressions.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Text.RegularExpressions.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/compressed/q7urm641t1.gz", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Security.Cryptography.OpenSsl.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Security.Cryptography.OpenSsl.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Security.Cryptography.OpenSsl.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/compressed/qntivumri3.gz", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Xml.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Xml.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Xml.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/compressed/qpprsitn5l.gz", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Reflection.Emit.ILGeneration.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Reflection.Emit.ILGeneration.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Reflection.Emit.ILGeneration.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/compressed/qxc3tmwp65.gz", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.IO.Pipelines.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.IO.Pipelines.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.IO.Pipelines.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/compressed/raqw9ibga8.gz", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.Configuration.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/Microsoft.Extensions.Configuration.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/Microsoft.Extensions.Configuration.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/compressed/rdchpfnf4s.gz", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Diagnostics.Tools.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Diagnostics.Tools.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Diagnostics.Tools.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/compressed/rnj0vqen2t.gz", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Configuration.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Configuration.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Configuration.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/compressed/rrv9l33ite.gz", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Net.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Net.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Net.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/compressed/rtaevpbbmw.gz", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Transactions.Local.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Transactions.Local.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Transactions.Local.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/compressed/s41zox1ddr.gz", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/UserManagement.Client.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/UserManagement.Client.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/UserManagement.Client.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/compressed/s96ggrvzlz.gz", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Reflection.Metadata.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Reflection.Metadata.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Reflection.Metadata.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/compressed/sfn0yhwd6s.gz", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Private.CoreLib.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Private.CoreLib.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Private.CoreLib.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/compressed/stf0njazg6.gz", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Runtime.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Runtime.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Runtime.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/compressed/su7bymvnje.gz", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Runtime.Serialization.Formatters.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Runtime.Serialization.Formatters.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Runtime.Serialization.Formatters.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/compressed/tcc9urxxaq.gz", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Net.WebHeaderCollection.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Net.WebHeaderCollection.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Net.WebHeaderCollection.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/compressed/tcn14l9l81.gz", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Reflection.Emit.Lightweight.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Reflection.Emit.Lightweight.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Reflection.Emit.Lightweight.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/compressed/tniy75odeg.gz", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Security.Principal.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Security.Principal.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Security.Principal.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/compressed/toyy6cxpm3.gz", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Reflection.Primitives.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Reflection.Primitives.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Reflection.Primitives.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/compressed/tt37ngsx14.gz", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Data.DataSetExtensions.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Data.DataSetExtensions.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Data.DataSetExtensions.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/compressed/tu9bdhhbae.gz", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.Configuration.Json.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/Microsoft.Extensions.Configuration.Json.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/Microsoft.Extensions.Configuration.Json.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/compressed/uak5zu1jl9.gz", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.Configuration.Binder.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/Microsoft.Extensions.Configuration.Binder.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/Microsoft.Extensions.Configuration.Binder.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/compressed/ud7e62pq5q.gz", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Collections.Immutable.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Collections.Immutable.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Collections.Immutable.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/compressed/ulxyikrc7e.gz", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/Microsoft.AspNetCore.Metadata.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/Microsoft.AspNetCore.Metadata.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/Microsoft.AspNetCore.Metadata.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/compressed/un1p5ijkyh.gz", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Globalization.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Globalization.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Globalization.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/compressed/uncgwp2i8k.gz", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Formats.Tar.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Formats.Tar.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Formats.Tar.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/compressed/up6202ky21.gz", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Xml.Linq.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Xml.Linq.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Xml.Linq.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/compressed/uvt8umbdge.gz", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Resources.Writer.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Resources.Writer.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Resources.Writer.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/compressed/ux7jd1jjvy.gz", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Xml.Serialization.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Xml.Serialization.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Xml.Serialization.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/compressed/uxv404eai0.gz", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/Microsoft.AspNetCore.Components.WebAssembly.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/Microsoft.AspNetCore.Components.WebAssembly.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/Microsoft.AspNetCore.Components.WebAssembly.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/compressed/vhicvzbu62.gz", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/Microsoft.JSInterop.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/Microsoft.JSInterop.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/Microsoft.JSInterop.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/compressed/vinl53llsd.gz", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Data.Common.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Data.Common.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Data.Common.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/compressed/vno9wkbjxv.gz", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Threading.Channels.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Threading.Channels.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Threading.Channels.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/compressed/vzn51mzruk.gz", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Net.NameResolution.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Net.NameResolution.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Net.NameResolution.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/compressed/w25390dz2f.gz", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Linq.Parallel.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Linq.Parallel.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Linq.Parallel.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/compressed/w9ye9s9y5q.gz", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Xml.ReaderWriter.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Xml.ReaderWriter.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Xml.ReaderWriter.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/compressed/wdokhvce2w.gz", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.IO.FileSystem.DriveInfo.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.IO.FileSystem.DriveInfo.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.IO.FileSystem.DriveInfo.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/compressed/we3g2ap95t.gz", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Runtime.InteropServices.RuntimeInformation.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Runtime.InteropServices.RuntimeInformation.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Runtime.InteropServices.RuntimeInformation.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/compressed/wfzdhshma4.gz", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Drawing.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Drawing.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Drawing.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/compressed/wgemfdv799.gz", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Globalization.Extensions.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Globalization.Extensions.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Globalization.Extensions.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/compressed/wmyw52o1k5.gz", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Security.Cryptography.Primitives.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Security.Cryptography.Primitives.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Security.Cryptography.Primitives.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/compressed/wq0v4e9vpo.gz", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Net.Http.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Net.Http.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Net.Http.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/compressed/wskccd8hb7.gz", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Net.Sockets.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Net.Sockets.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Net.Sockets.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/compressed/x22uwg6m7g.gz", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Security.SecureString.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Security.SecureString.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Security.SecureString.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/compressed/x2dk26qk8b.gz", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Reflection.Extensions.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Reflection.Extensions.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Reflection.Extensions.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/compressed/x3ww0hthua.gz", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Xml.XPath.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Xml.XPath.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Xml.XPath.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/compressed/x7zyfrygvv.gz", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Diagnostics.DiagnosticSource.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Diagnostics.DiagnosticSource.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Diagnostics.DiagnosticSource.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/compressed/xij4c7qgjx.gz", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.IO.Pipes.AccessControl.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.IO.Pipes.AccessControl.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.IO.Pipes.AccessControl.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/compressed/xvo9ja0o4h.gz", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Collections.Specialized.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Collections.Specialized.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Collections.Specialized.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/compressed/y5r067ioj4.gz", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.DependencyInjection.Abstractions.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/Microsoft.Extensions.DependencyInjection.Abstractions.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/Microsoft.Extensions.DependencyInjection.Abstractions.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/compressed/yydjer1kdj.gz", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Net.WebSockets.Client.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Net.WebSockets.Client.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Net.WebSockets.Client.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/compressed/z0zkfutwsy.gz", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Linq.Expressions.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Linq.Expressions.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Linq.Expressions.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/compressed/z6jvgowxb5.gz", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Security.Principal.Windows.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Security.Principal.Windows.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Security.Principal.Windows.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/compressed/zezo62g8kn.gz", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Reflection.DispatchProxy.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Reflection.DispatchProxy.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Reflection.DispatchProxy.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/compressed/zi08v745um.gz", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.AppContext.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.AppContext.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.AppContext.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/compressed/zll3vemnfx.gz", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Threading.Tasks.Parallel.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Threading.Tasks.Parallel.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Threading.Tasks.Parallel.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/compressed/zut9de47ww.gz", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/blazor.boot.json.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/blazor.boot.json", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/blazor.boot.json"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/compressed/zveylo35ip.gz", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/System.Diagnostics.Tracing.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Diagnostics.Tracing.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/System.Diagnostics.Tracing.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/compressed/zy7v4d5k58.gz", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "BasePath": "/", "RelativePath": "_framework/Microsoft.VisualBasic.Core.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/Microsoft.VisualBasic.Core.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/Microsoft.VisualBasic.Core.wasm"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/scopedcss/bundle/UserManagement.Client.styles.css", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/scopedcss/bundle/", "BasePath": "/", "RelativePath": "UserManagement.Client.styles.css", "AssetKind": "All", "AssetMode": "CurrentProject", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "ScopedCss", "AssetTraitValue": "ApplicationBundle", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/scopedcss/bundle/UserManagement.Client.styles.css"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/scopedcss/projectbundle/UserManagement.Client.bundle.scp.css", "SourceId": "UserManagement.Client", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/scopedcss/projectbundle/", "BasePath": "/", "RelativePath": "UserManagement.Client.bundle.scp.css", "AssetKind": "All", "AssetMode": "Reference", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "ScopedCss", "AssetTraitValue": "ProjectBundle", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/scopedcss/projectbundle/UserManagement.Client.bundle.scp.css"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/wwwroot/css/app.css", "SourceId": "UserManagement.Client", "SourceType": "Discovered", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/wwwroot/", "BasePath": "/", "RelativePath": "css/app.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/css/app.css"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/wwwroot/css/bootstrap/bootstrap.min.css", "SourceId": "UserManagement.Client", "SourceType": "Discovered", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/wwwroot/", "BasePath": "/", "RelativePath": "css/bootstrap/bootstrap.min.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/css/bootstrap/bootstrap.min.css"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/wwwroot/css/bootstrap/bootstrap.min.css.map", "SourceId": "UserManagement.Client", "SourceType": "Discovered", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/wwwroot/", "BasePath": "/", "RelativePath": "css/bootstrap/bootstrap.min.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/css/bootstrap/bootstrap.min.css.map"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/wwwroot/favicon.png", "SourceId": "UserManagement.Client", "SourceType": "Discovered", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/wwwroot/", "BasePath": "/", "RelativePath": "favicon.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/favicon.png"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/wwwroot/icon-192.png", "SourceId": "UserManagement.Client", "SourceType": "Discovered", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/wwwroot/", "BasePath": "/", "RelativePath": "icon-192.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/icon-192.png"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/wwwroot/index.html", "SourceId": "UserManagement.Client", "SourceType": "Discovered", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/wwwroot/", "BasePath": "/", "RelativePath": "index.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/index.html"}, {"Identity": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/wwwroot/sample-data/weather.json", "SourceId": "UserManagement.Client", "SourceType": "Discovered", "ContentRoot": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/wwwroot/", "BasePath": "/", "RelativePath": "sample-data/weather.json", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/sample-data/weather.json"}]}