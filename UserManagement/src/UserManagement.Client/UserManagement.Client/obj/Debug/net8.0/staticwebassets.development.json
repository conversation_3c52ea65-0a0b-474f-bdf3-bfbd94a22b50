{"ContentRoots": ["/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/wwwroot/", "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/", "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/scopedcss/bundle/"], "Root": {"Children": {"css": {"Children": {"app.css": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "css/app.css"}, "Patterns": null}, "bootstrap": {"Children": {"bootstrap.min.css": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "css/bootstrap/bootstrap.min.css"}, "Patterns": null}, "bootstrap.min.css.map": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "css/bootstrap/bootstrap.min.css.map"}, "Patterns": null}}, "Asset": null, "Patterns": null}}, "Asset": null, "Patterns": null}, "favicon.png": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "favicon.png"}, "Patterns": null}, "icon-192.png": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "icon-192.png"}, "Patterns": null}, "index.html": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "index.html"}, "Patterns": null}, "sample-data": {"Children": {"weather.json": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "sample-data/weather.json"}, "Patterns": null}}, "Asset": null, "Patterns": null}, "_framework": {"Children": {"blazor.webassembly.js": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/blazor.webassembly.js"}, "Patterns": null}, "Microsoft.AspNetCore.Authorization.wasm": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/Microsoft.AspNetCore.Authorization.wasm"}, "Patterns": null}, "Microsoft.AspNetCore.Components.wasm": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/Microsoft.AspNetCore.Components.wasm"}, "Patterns": null}, "Microsoft.AspNetCore.Components.Forms.wasm": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/Microsoft.AspNetCore.Components.Forms.wasm"}, "Patterns": null}, "Microsoft.AspNetCore.Components.Web.wasm": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/Microsoft.AspNetCore.Components.Web.wasm"}, "Patterns": null}, "Microsoft.AspNetCore.Components.WebAssembly.wasm": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/Microsoft.AspNetCore.Components.WebAssembly.wasm"}, "Patterns": null}, "Microsoft.AspNetCore.Metadata.wasm": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/Microsoft.AspNetCore.Metadata.wasm"}, "Patterns": null}, "Microsoft.Extensions.Configuration.wasm": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/Microsoft.Extensions.Configuration.wasm"}, "Patterns": null}, "Microsoft.Extensions.Configuration.Abstractions.wasm": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/Microsoft.Extensions.Configuration.Abstractions.wasm"}, "Patterns": null}, "Microsoft.Extensions.Configuration.Binder.wasm": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/Microsoft.Extensions.Configuration.Binder.wasm"}, "Patterns": null}, "Microsoft.Extensions.Configuration.FileExtensions.wasm": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/Microsoft.Extensions.Configuration.FileExtensions.wasm"}, "Patterns": null}, "Microsoft.Extensions.Configuration.Json.wasm": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/Microsoft.Extensions.Configuration.Json.wasm"}, "Patterns": null}, "Microsoft.Extensions.DependencyInjection.wasm": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/Microsoft.Extensions.DependencyInjection.wasm"}, "Patterns": null}, "Microsoft.Extensions.DependencyInjection.Abstractions.wasm": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/Microsoft.Extensions.DependencyInjection.Abstractions.wasm"}, "Patterns": null}, "Microsoft.Extensions.FileProviders.Abstractions.wasm": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/Microsoft.Extensions.FileProviders.Abstractions.wasm"}, "Patterns": null}, "Microsoft.Extensions.FileProviders.Physical.wasm": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/Microsoft.Extensions.FileProviders.Physical.wasm"}, "Patterns": null}, "Microsoft.Extensions.FileSystemGlobbing.wasm": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/Microsoft.Extensions.FileSystemGlobbing.wasm"}, "Patterns": null}, "Microsoft.Extensions.Logging.wasm": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/Microsoft.Extensions.Logging.wasm"}, "Patterns": null}, "Microsoft.Extensions.Logging.Abstractions.wasm": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/Microsoft.Extensions.Logging.Abstractions.wasm"}, "Patterns": null}, "Microsoft.Extensions.Options.wasm": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/Microsoft.Extensions.Options.wasm"}, "Patterns": null}, "Microsoft.Extensions.Primitives.wasm": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/Microsoft.Extensions.Primitives.wasm"}, "Patterns": null}, "Microsoft.JSInterop.wasm": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/Microsoft.JSInterop.wasm"}, "Patterns": null}, "Microsoft.JSInterop.WebAssembly.wasm": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/Microsoft.JSInterop.WebAssembly.wasm"}, "Patterns": null}, "System.IO.Pipelines.wasm": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.IO.Pipelines.wasm"}, "Patterns": null}, "Microsoft.CSharp.wasm": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/Microsoft.CSharp.wasm"}, "Patterns": null}, "Microsoft.VisualBasic.Core.wasm": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/Microsoft.VisualBasic.Core.wasm"}, "Patterns": null}, "Microsoft.VisualBasic.wasm": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/Microsoft.VisualBasic.wasm"}, "Patterns": null}, "Microsoft.Win32.Primitives.wasm": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/Microsoft.Win32.Primitives.wasm"}, "Patterns": null}, "Microsoft.Win32.Registry.wasm": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/Microsoft.Win32.Registry.wasm"}, "Patterns": null}, "System.AppContext.wasm": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.AppContext.wasm"}, "Patterns": null}, "System.Buffers.wasm": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Buffers.wasm"}, "Patterns": null}, "System.Collections.Concurrent.wasm": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Collections.Concurrent.wasm"}, "Patterns": null}, "System.Collections.Immutable.wasm": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Collections.Immutable.wasm"}, "Patterns": null}, "System.Collections.NonGeneric.wasm": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Collections.NonGeneric.wasm"}, "Patterns": null}, "System.Collections.Specialized.wasm": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Collections.Specialized.wasm"}, "Patterns": null}, "System.Collections.wasm": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Collections.wasm"}, "Patterns": null}, "System.ComponentModel.Annotations.wasm": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.ComponentModel.Annotations.wasm"}, "Patterns": null}, "System.ComponentModel.DataAnnotations.wasm": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.ComponentModel.DataAnnotations.wasm"}, "Patterns": null}, "System.ComponentModel.EventBasedAsync.wasm": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.ComponentModel.EventBasedAsync.wasm"}, "Patterns": null}, "System.ComponentModel.Primitives.wasm": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.ComponentModel.Primitives.wasm"}, "Patterns": null}, "System.ComponentModel.TypeConverter.wasm": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.ComponentModel.TypeConverter.wasm"}, "Patterns": null}, "System.ComponentModel.wasm": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.ComponentModel.wasm"}, "Patterns": null}, "System.Configuration.wasm": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Configuration.wasm"}, "Patterns": null}, "System.Console.wasm": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Console.wasm"}, "Patterns": null}, "System.Core.wasm": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Core.wasm"}, "Patterns": null}, "System.Data.Common.wasm": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Data.Common.wasm"}, "Patterns": null}, "System.Data.DataSetExtensions.wasm": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Data.DataSetExtensions.wasm"}, "Patterns": null}, "System.Data.wasm": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Data.wasm"}, "Patterns": null}, "System.Diagnostics.Contracts.wasm": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Diagnostics.Contracts.wasm"}, "Patterns": null}, "System.Diagnostics.Debug.wasm": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Diagnostics.Debug.wasm"}, "Patterns": null}, "System.Diagnostics.DiagnosticSource.wasm": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Diagnostics.DiagnosticSource.wasm"}, "Patterns": null}, "System.Diagnostics.FileVersionInfo.wasm": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Diagnostics.FileVersionInfo.wasm"}, "Patterns": null}, "System.Diagnostics.Process.wasm": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Diagnostics.Process.wasm"}, "Patterns": null}, "System.Diagnostics.StackTrace.wasm": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Diagnostics.StackTrace.wasm"}, "Patterns": null}, "System.Diagnostics.TextWriterTraceListener.wasm": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Diagnostics.TextWriterTraceListener.wasm"}, "Patterns": null}, "System.Diagnostics.Tools.wasm": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Diagnostics.Tools.wasm"}, "Patterns": null}, "System.Diagnostics.TraceSource.wasm": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Diagnostics.TraceSource.wasm"}, "Patterns": null}, "System.Diagnostics.Tracing.wasm": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Diagnostics.Tracing.wasm"}, "Patterns": null}, "System.Drawing.Primitives.wasm": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Drawing.Primitives.wasm"}, "Patterns": null}, "System.Drawing.wasm": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Drawing.wasm"}, "Patterns": null}, "System.Dynamic.Runtime.wasm": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Dynamic.Runtime.wasm"}, "Patterns": null}, "System.Formats.Asn1.wasm": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Formats.Asn1.wasm"}, "Patterns": null}, "System.Formats.Tar.wasm": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Formats.Tar.wasm"}, "Patterns": null}, "System.Globalization.Calendars.wasm": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Globalization.Calendars.wasm"}, "Patterns": null}, "System.Globalization.Extensions.wasm": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Globalization.Extensions.wasm"}, "Patterns": null}, "System.Globalization.wasm": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Globalization.wasm"}, "Patterns": null}, "System.IO.Compression.Brotli.wasm": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.IO.Compression.Brotli.wasm"}, "Patterns": null}, "System.IO.Compression.FileSystem.wasm": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.IO.Compression.FileSystem.wasm"}, "Patterns": null}, "System.IO.Compression.ZipFile.wasm": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.IO.Compression.ZipFile.wasm"}, "Patterns": null}, "System.IO.Compression.wasm": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.IO.Compression.wasm"}, "Patterns": null}, "System.IO.FileSystem.AccessControl.wasm": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.IO.FileSystem.AccessControl.wasm"}, "Patterns": null}, "System.IO.FileSystem.DriveInfo.wasm": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.IO.FileSystem.DriveInfo.wasm"}, "Patterns": null}, "System.IO.FileSystem.Primitives.wasm": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.IO.FileSystem.Primitives.wasm"}, "Patterns": null}, "System.IO.FileSystem.Watcher.wasm": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.IO.FileSystem.Watcher.wasm"}, "Patterns": null}, "System.IO.FileSystem.wasm": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.IO.FileSystem.wasm"}, "Patterns": null}, "System.IO.IsolatedStorage.wasm": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.IO.IsolatedStorage.wasm"}, "Patterns": null}, "System.IO.MemoryMappedFiles.wasm": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.IO.MemoryMappedFiles.wasm"}, "Patterns": null}, "System.IO.Pipes.AccessControl.wasm": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.IO.Pipes.AccessControl.wasm"}, "Patterns": null}, "System.IO.Pipes.wasm": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.IO.Pipes.wasm"}, "Patterns": null}, "System.IO.UnmanagedMemoryStream.wasm": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.IO.UnmanagedMemoryStream.wasm"}, "Patterns": null}, "System.IO.wasm": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.IO.wasm"}, "Patterns": null}, "System.Linq.Expressions.wasm": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Linq.Expressions.wasm"}, "Patterns": null}, "System.Linq.Parallel.wasm": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Linq.Parallel.wasm"}, "Patterns": null}, "System.Linq.Queryable.wasm": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Linq.Queryable.wasm"}, "Patterns": null}, "System.Linq.wasm": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Linq.wasm"}, "Patterns": null}, "System.Memory.wasm": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Memory.wasm"}, "Patterns": null}, "System.Net.Http.Json.wasm": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Net.Http.Json.wasm"}, "Patterns": null}, "System.Net.Http.wasm": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Net.Http.wasm"}, "Patterns": null}, "System.Net.HttpListener.wasm": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Net.HttpListener.wasm"}, "Patterns": null}, "System.Net.Mail.wasm": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Net.Mail.wasm"}, "Patterns": null}, "System.Net.NameResolution.wasm": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Net.NameResolution.wasm"}, "Patterns": null}, "System.Net.NetworkInformation.wasm": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Net.NetworkInformation.wasm"}, "Patterns": null}, "System.Net.Ping.wasm": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Net.Ping.wasm"}, "Patterns": null}, "System.Net.Primitives.wasm": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Net.Primitives.wasm"}, "Patterns": null}, "System.Net.Quic.wasm": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Net.Quic.wasm"}, "Patterns": null}, "System.Net.Requests.wasm": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Net.Requests.wasm"}, "Patterns": null}, "System.Net.Security.wasm": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Net.Security.wasm"}, "Patterns": null}, "System.Net.ServicePoint.wasm": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Net.ServicePoint.wasm"}, "Patterns": null}, "System.Net.Sockets.wasm": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Net.Sockets.wasm"}, "Patterns": null}, "System.Net.WebClient.wasm": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Net.WebClient.wasm"}, "Patterns": null}, "System.Net.WebHeaderCollection.wasm": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Net.WebHeaderCollection.wasm"}, "Patterns": null}, "System.Net.WebProxy.wasm": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Net.WebProxy.wasm"}, "Patterns": null}, "System.Net.WebSockets.Client.wasm": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Net.WebSockets.Client.wasm"}, "Patterns": null}, "System.Net.WebSockets.wasm": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Net.WebSockets.wasm"}, "Patterns": null}, "System.Net.wasm": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Net.wasm"}, "Patterns": null}, "System.Numerics.Vectors.wasm": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Numerics.Vectors.wasm"}, "Patterns": null}, "System.Numerics.wasm": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Numerics.wasm"}, "Patterns": null}, "System.ObjectModel.wasm": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.ObjectModel.wasm"}, "Patterns": null}, "System.Private.DataContractSerialization.wasm": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Private.DataContractSerialization.wasm"}, "Patterns": null}, "System.Private.Uri.wasm": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Private.Uri.wasm"}, "Patterns": null}, "System.Private.Xml.Linq.wasm": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Private.Xml.Linq.wasm"}, "Patterns": null}, "System.Private.Xml.wasm": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Private.Xml.wasm"}, "Patterns": null}, "System.Reflection.DispatchProxy.wasm": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Reflection.DispatchProxy.wasm"}, "Patterns": null}, "System.Reflection.Emit.ILGeneration.wasm": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Reflection.Emit.ILGeneration.wasm"}, "Patterns": null}, "System.Reflection.Emit.Lightweight.wasm": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Reflection.Emit.Lightweight.wasm"}, "Patterns": null}, "System.Reflection.Emit.wasm": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Reflection.Emit.wasm"}, "Patterns": null}, "System.Reflection.Extensions.wasm": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Reflection.Extensions.wasm"}, "Patterns": null}, "System.Reflection.Metadata.wasm": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Reflection.Metadata.wasm"}, "Patterns": null}, "System.Reflection.Primitives.wasm": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Reflection.Primitives.wasm"}, "Patterns": null}, "System.Reflection.TypeExtensions.wasm": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Reflection.TypeExtensions.wasm"}, "Patterns": null}, "System.Reflection.wasm": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Reflection.wasm"}, "Patterns": null}, "System.Resources.Reader.wasm": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Resources.Reader.wasm"}, "Patterns": null}, "System.Resources.ResourceManager.wasm": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Resources.ResourceManager.wasm"}, "Patterns": null}, "System.Resources.Writer.wasm": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Resources.Writer.wasm"}, "Patterns": null}, "System.Runtime.CompilerServices.Unsafe.wasm": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Runtime.CompilerServices.Unsafe.wasm"}, "Patterns": null}, "System.Runtime.CompilerServices.VisualC.wasm": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Runtime.CompilerServices.VisualC.wasm"}, "Patterns": null}, "System.Runtime.Extensions.wasm": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Runtime.Extensions.wasm"}, "Patterns": null}, "System.Runtime.Handles.wasm": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Runtime.Handles.wasm"}, "Patterns": null}, "System.Runtime.InteropServices.JavaScript.wasm": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Runtime.InteropServices.JavaScript.wasm"}, "Patterns": null}, "System.Runtime.InteropServices.RuntimeInformation.wasm": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Runtime.InteropServices.RuntimeInformation.wasm"}, "Patterns": null}, "System.Runtime.InteropServices.wasm": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Runtime.InteropServices.wasm"}, "Patterns": null}, "System.Runtime.Intrinsics.wasm": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Runtime.Intrinsics.wasm"}, "Patterns": null}, "System.Runtime.Loader.wasm": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Runtime.Loader.wasm"}, "Patterns": null}, "System.Runtime.Numerics.wasm": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Runtime.Numerics.wasm"}, "Patterns": null}, "System.Runtime.Serialization.Formatters.wasm": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Runtime.Serialization.Formatters.wasm"}, "Patterns": null}, "System.Runtime.Serialization.Json.wasm": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Runtime.Serialization.Json.wasm"}, "Patterns": null}, "System.Runtime.Serialization.Primitives.wasm": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Runtime.Serialization.Primitives.wasm"}, "Patterns": null}, "System.Runtime.Serialization.Xml.wasm": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Runtime.Serialization.Xml.wasm"}, "Patterns": null}, "System.Runtime.Serialization.wasm": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Runtime.Serialization.wasm"}, "Patterns": null}, "System.Runtime.wasm": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Runtime.wasm"}, "Patterns": null}, "System.Security.AccessControl.wasm": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Security.AccessControl.wasm"}, "Patterns": null}, "System.Security.Claims.wasm": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Security.Claims.wasm"}, "Patterns": null}, "System.Security.Cryptography.Algorithms.wasm": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Security.Cryptography.Algorithms.wasm"}, "Patterns": null}, "System.Security.Cryptography.Cng.wasm": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Security.Cryptography.Cng.wasm"}, "Patterns": null}, "System.Security.Cryptography.Csp.wasm": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Security.Cryptography.Csp.wasm"}, "Patterns": null}, "System.Security.Cryptography.Encoding.wasm": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Security.Cryptography.Encoding.wasm"}, "Patterns": null}, "System.Security.Cryptography.OpenSsl.wasm": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Security.Cryptography.OpenSsl.wasm"}, "Patterns": null}, "System.Security.Cryptography.Primitives.wasm": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Security.Cryptography.Primitives.wasm"}, "Patterns": null}, "System.Security.Cryptography.X509Certificates.wasm": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Security.Cryptography.X509Certificates.wasm"}, "Patterns": null}, "System.Security.Cryptography.wasm": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Security.Cryptography.wasm"}, "Patterns": null}, "System.Security.Principal.Windows.wasm": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Security.Principal.Windows.wasm"}, "Patterns": null}, "System.Security.Principal.wasm": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Security.Principal.wasm"}, "Patterns": null}, "System.Security.SecureString.wasm": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Security.SecureString.wasm"}, "Patterns": null}, "System.Security.wasm": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Security.wasm"}, "Patterns": null}, "System.ServiceModel.Web.wasm": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.ServiceModel.Web.wasm"}, "Patterns": null}, "System.ServiceProcess.wasm": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.ServiceProcess.wasm"}, "Patterns": null}, "System.Text.Encoding.CodePages.wasm": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Text.Encoding.CodePages.wasm"}, "Patterns": null}, "System.Text.Encoding.Extensions.wasm": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Text.Encoding.Extensions.wasm"}, "Patterns": null}, "System.Text.Encoding.wasm": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Text.Encoding.wasm"}, "Patterns": null}, "System.Text.Encodings.Web.wasm": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Text.Encodings.Web.wasm"}, "Patterns": null}, "System.Text.Json.wasm": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Text.Json.wasm"}, "Patterns": null}, "System.Text.RegularExpressions.wasm": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Text.RegularExpressions.wasm"}, "Patterns": null}, "System.Threading.Channels.wasm": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Threading.Channels.wasm"}, "Patterns": null}, "System.Threading.Overlapped.wasm": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Threading.Overlapped.wasm"}, "Patterns": null}, "System.Threading.Tasks.Dataflow.wasm": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Threading.Tasks.Dataflow.wasm"}, "Patterns": null}, "System.Threading.Tasks.Extensions.wasm": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Threading.Tasks.Extensions.wasm"}, "Patterns": null}, "System.Threading.Tasks.Parallel.wasm": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Threading.Tasks.Parallel.wasm"}, "Patterns": null}, "System.Threading.Tasks.wasm": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Threading.Tasks.wasm"}, "Patterns": null}, "System.Threading.Thread.wasm": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Threading.Thread.wasm"}, "Patterns": null}, "System.Threading.ThreadPool.wasm": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Threading.ThreadPool.wasm"}, "Patterns": null}, "System.Threading.Timer.wasm": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Threading.Timer.wasm"}, "Patterns": null}, "System.Threading.wasm": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Threading.wasm"}, "Patterns": null}, "System.Transactions.Local.wasm": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Transactions.Local.wasm"}, "Patterns": null}, "System.Transactions.wasm": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Transactions.wasm"}, "Patterns": null}, "System.ValueTuple.wasm": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.ValueTuple.wasm"}, "Patterns": null}, "System.Web.HttpUtility.wasm": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Web.HttpUtility.wasm"}, "Patterns": null}, "System.Web.wasm": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Web.wasm"}, "Patterns": null}, "System.Windows.wasm": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Windows.wasm"}, "Patterns": null}, "System.Xml.Linq.wasm": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Xml.Linq.wasm"}, "Patterns": null}, "System.Xml.ReaderWriter.wasm": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Xml.ReaderWriter.wasm"}, "Patterns": null}, "System.Xml.Serialization.wasm": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Xml.Serialization.wasm"}, "Patterns": null}, "System.Xml.XDocument.wasm": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Xml.XDocument.wasm"}, "Patterns": null}, "System.Xml.XPath.XDocument.wasm": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Xml.XPath.XDocument.wasm"}, "Patterns": null}, "System.Xml.XPath.wasm": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Xml.XPath.wasm"}, "Patterns": null}, "System.Xml.XmlDocument.wasm": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Xml.XmlDocument.wasm"}, "Patterns": null}, "System.Xml.XmlSerializer.wasm": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Xml.XmlSerializer.wasm"}, "Patterns": null}, "System.Xml.wasm": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Xml.wasm"}, "Patterns": null}, "System.wasm": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.wasm"}, "Patterns": null}, "WindowsBase.wasm": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/WindowsBase.wasm"}, "Patterns": null}, "mscorlib.wasm": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/mscorlib.wasm"}, "Patterns": null}, "netstandard.wasm": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/netstandard.wasm"}, "Patterns": null}, "System.Private.CoreLib.wasm": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Private.CoreLib.wasm"}, "Patterns": null}, "dotnet.js": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/dotnet.js"}, "Patterns": null}, "dotnet.js.map": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/dotnet.js.map"}, "Patterns": null}, "dotnet.native.8.0.20.frvb3disbh.js": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/dotnet.native.8.0.20.frvb3disbh.js"}, "Patterns": null}, "dotnet.native.wasm": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/dotnet.native.wasm"}, "Patterns": null}, "dotnet.runtime.8.0.20.gped7iqcy4.js": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/dotnet.runtime.8.0.20.gped7iqcy4.js"}, "Patterns": null}, "dotnet.runtime.js.map": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/dotnet.runtime.js.map"}, "Patterns": null}, "icudt_CJK.dat": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/icudt_CJK.dat"}, "Patterns": null}, "icudt_EFIGS.dat": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/icudt_EFIGS.dat"}, "Patterns": null}, "icudt_no_CJK.dat": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/icudt_no_CJK.dat"}, "Patterns": null}, "UserManagement.Shared.wasm": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/UserManagement.Shared.wasm"}, "Patterns": null}, "UserManagement.Shared.pdb": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/UserManagement.Shared.pdb"}, "Patterns": null}, "UserManagement.Client.wasm": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/UserManagement.Client.wasm"}, "Patterns": null}, "UserManagement.Client.pdb": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/UserManagement.Client.pdb"}, "Patterns": null}, "blazor.boot.json": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/blazor.boot.json"}, "Patterns": null}, "blazor.webassembly.js.gz": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/blazor.webassembly.js.gz"}, "Patterns": null}, "Microsoft.AspNetCore.Authorization.wasm.gz": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/Microsoft.AspNetCore.Authorization.wasm.gz"}, "Patterns": null}, "Microsoft.AspNetCore.Components.wasm.gz": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/Microsoft.AspNetCore.Components.wasm.gz"}, "Patterns": null}, "Microsoft.AspNetCore.Components.Forms.wasm.gz": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/Microsoft.AspNetCore.Components.Forms.wasm.gz"}, "Patterns": null}, "Microsoft.AspNetCore.Components.Web.wasm.gz": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/Microsoft.AspNetCore.Components.Web.wasm.gz"}, "Patterns": null}, "Microsoft.AspNetCore.Components.WebAssembly.wasm.gz": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/Microsoft.AspNetCore.Components.WebAssembly.wasm.gz"}, "Patterns": null}, "Microsoft.AspNetCore.Metadata.wasm.gz": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/Microsoft.AspNetCore.Metadata.wasm.gz"}, "Patterns": null}, "Microsoft.Extensions.Configuration.wasm.gz": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/Microsoft.Extensions.Configuration.wasm.gz"}, "Patterns": null}, "Microsoft.Extensions.Configuration.Abstractions.wasm.gz": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/Microsoft.Extensions.Configuration.Abstractions.wasm.gz"}, "Patterns": null}, "Microsoft.Extensions.Configuration.Binder.wasm.gz": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/Microsoft.Extensions.Configuration.Binder.wasm.gz"}, "Patterns": null}, "Microsoft.Extensions.Configuration.FileExtensions.wasm.gz": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/Microsoft.Extensions.Configuration.FileExtensions.wasm.gz"}, "Patterns": null}, "Microsoft.Extensions.Configuration.Json.wasm.gz": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/Microsoft.Extensions.Configuration.Json.wasm.gz"}, "Patterns": null}, "Microsoft.Extensions.DependencyInjection.wasm.gz": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/Microsoft.Extensions.DependencyInjection.wasm.gz"}, "Patterns": null}, "Microsoft.Extensions.DependencyInjection.Abstractions.wasm.gz": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/Microsoft.Extensions.DependencyInjection.Abstractions.wasm.gz"}, "Patterns": null}, "Microsoft.Extensions.FileProviders.Abstractions.wasm.gz": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/Microsoft.Extensions.FileProviders.Abstractions.wasm.gz"}, "Patterns": null}, "Microsoft.Extensions.FileProviders.Physical.wasm.gz": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/Microsoft.Extensions.FileProviders.Physical.wasm.gz"}, "Patterns": null}, "Microsoft.Extensions.FileSystemGlobbing.wasm.gz": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/Microsoft.Extensions.FileSystemGlobbing.wasm.gz"}, "Patterns": null}, "Microsoft.Extensions.Logging.wasm.gz": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/Microsoft.Extensions.Logging.wasm.gz"}, "Patterns": null}, "Microsoft.Extensions.Logging.Abstractions.wasm.gz": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/Microsoft.Extensions.Logging.Abstractions.wasm.gz"}, "Patterns": null}, "Microsoft.Extensions.Options.wasm.gz": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/Microsoft.Extensions.Options.wasm.gz"}, "Patterns": null}, "Microsoft.Extensions.Primitives.wasm.gz": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/Microsoft.Extensions.Primitives.wasm.gz"}, "Patterns": null}, "Microsoft.JSInterop.wasm.gz": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/Microsoft.JSInterop.wasm.gz"}, "Patterns": null}, "Microsoft.JSInterop.WebAssembly.wasm.gz": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/Microsoft.JSInterop.WebAssembly.wasm.gz"}, "Patterns": null}, "System.IO.Pipelines.wasm.gz": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.IO.Pipelines.wasm.gz"}, "Patterns": null}, "Microsoft.CSharp.wasm.gz": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/Microsoft.CSharp.wasm.gz"}, "Patterns": null}, "Microsoft.VisualBasic.Core.wasm.gz": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/Microsoft.VisualBasic.Core.wasm.gz"}, "Patterns": null}, "Microsoft.VisualBasic.wasm.gz": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/Microsoft.VisualBasic.wasm.gz"}, "Patterns": null}, "Microsoft.Win32.Primitives.wasm.gz": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/Microsoft.Win32.Primitives.wasm.gz"}, "Patterns": null}, "Microsoft.Win32.Registry.wasm.gz": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/Microsoft.Win32.Registry.wasm.gz"}, "Patterns": null}, "System.AppContext.wasm.gz": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.AppContext.wasm.gz"}, "Patterns": null}, "System.Buffers.wasm.gz": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Buffers.wasm.gz"}, "Patterns": null}, "System.Collections.Concurrent.wasm.gz": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Collections.Concurrent.wasm.gz"}, "Patterns": null}, "System.Collections.Immutable.wasm.gz": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Collections.Immutable.wasm.gz"}, "Patterns": null}, "System.Collections.NonGeneric.wasm.gz": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Collections.NonGeneric.wasm.gz"}, "Patterns": null}, "System.Collections.Specialized.wasm.gz": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Collections.Specialized.wasm.gz"}, "Patterns": null}, "System.Collections.wasm.gz": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Collections.wasm.gz"}, "Patterns": null}, "System.ComponentModel.Annotations.wasm.gz": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.ComponentModel.Annotations.wasm.gz"}, "Patterns": null}, "System.ComponentModel.DataAnnotations.wasm.gz": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.ComponentModel.DataAnnotations.wasm.gz"}, "Patterns": null}, "System.ComponentModel.EventBasedAsync.wasm.gz": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.ComponentModel.EventBasedAsync.wasm.gz"}, "Patterns": null}, "System.ComponentModel.Primitives.wasm.gz": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.ComponentModel.Primitives.wasm.gz"}, "Patterns": null}, "System.ComponentModel.TypeConverter.wasm.gz": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.ComponentModel.TypeConverter.wasm.gz"}, "Patterns": null}, "System.ComponentModel.wasm.gz": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.ComponentModel.wasm.gz"}, "Patterns": null}, "System.Configuration.wasm.gz": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Configuration.wasm.gz"}, "Patterns": null}, "System.Console.wasm.gz": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Console.wasm.gz"}, "Patterns": null}, "System.Core.wasm.gz": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Core.wasm.gz"}, "Patterns": null}, "System.Data.Common.wasm.gz": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Data.Common.wasm.gz"}, "Patterns": null}, "System.Data.DataSetExtensions.wasm.gz": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Data.DataSetExtensions.wasm.gz"}, "Patterns": null}, "System.Data.wasm.gz": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Data.wasm.gz"}, "Patterns": null}, "System.Diagnostics.Contracts.wasm.gz": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Diagnostics.Contracts.wasm.gz"}, "Patterns": null}, "System.Diagnostics.Debug.wasm.gz": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Diagnostics.Debug.wasm.gz"}, "Patterns": null}, "System.Diagnostics.DiagnosticSource.wasm.gz": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Diagnostics.DiagnosticSource.wasm.gz"}, "Patterns": null}, "System.Diagnostics.FileVersionInfo.wasm.gz": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Diagnostics.FileVersionInfo.wasm.gz"}, "Patterns": null}, "System.Diagnostics.Process.wasm.gz": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Diagnostics.Process.wasm.gz"}, "Patterns": null}, "System.Diagnostics.StackTrace.wasm.gz": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Diagnostics.StackTrace.wasm.gz"}, "Patterns": null}, "System.Diagnostics.TextWriterTraceListener.wasm.gz": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Diagnostics.TextWriterTraceListener.wasm.gz"}, "Patterns": null}, "System.Diagnostics.Tools.wasm.gz": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Diagnostics.Tools.wasm.gz"}, "Patterns": null}, "System.Diagnostics.TraceSource.wasm.gz": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Diagnostics.TraceSource.wasm.gz"}, "Patterns": null}, "System.Diagnostics.Tracing.wasm.gz": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Diagnostics.Tracing.wasm.gz"}, "Patterns": null}, "System.Drawing.Primitives.wasm.gz": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Drawing.Primitives.wasm.gz"}, "Patterns": null}, "System.Drawing.wasm.gz": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Drawing.wasm.gz"}, "Patterns": null}, "System.Dynamic.Runtime.wasm.gz": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Dynamic.Runtime.wasm.gz"}, "Patterns": null}, "System.Formats.Asn1.wasm.gz": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Formats.Asn1.wasm.gz"}, "Patterns": null}, "System.Formats.Tar.wasm.gz": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Formats.Tar.wasm.gz"}, "Patterns": null}, "System.Globalization.Calendars.wasm.gz": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Globalization.Calendars.wasm.gz"}, "Patterns": null}, "System.Globalization.Extensions.wasm.gz": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Globalization.Extensions.wasm.gz"}, "Patterns": null}, "System.Globalization.wasm.gz": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Globalization.wasm.gz"}, "Patterns": null}, "System.IO.Compression.Brotli.wasm.gz": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.IO.Compression.Brotli.wasm.gz"}, "Patterns": null}, "System.IO.Compression.FileSystem.wasm.gz": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.IO.Compression.FileSystem.wasm.gz"}, "Patterns": null}, "System.IO.Compression.ZipFile.wasm.gz": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.IO.Compression.ZipFile.wasm.gz"}, "Patterns": null}, "System.IO.Compression.wasm.gz": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.IO.Compression.wasm.gz"}, "Patterns": null}, "System.IO.FileSystem.AccessControl.wasm.gz": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.IO.FileSystem.AccessControl.wasm.gz"}, "Patterns": null}, "System.IO.FileSystem.DriveInfo.wasm.gz": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.IO.FileSystem.DriveInfo.wasm.gz"}, "Patterns": null}, "System.IO.FileSystem.Primitives.wasm.gz": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.IO.FileSystem.Primitives.wasm.gz"}, "Patterns": null}, "System.IO.FileSystem.Watcher.wasm.gz": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.IO.FileSystem.Watcher.wasm.gz"}, "Patterns": null}, "System.IO.FileSystem.wasm.gz": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.IO.FileSystem.wasm.gz"}, "Patterns": null}, "System.IO.IsolatedStorage.wasm.gz": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.IO.IsolatedStorage.wasm.gz"}, "Patterns": null}, "System.IO.MemoryMappedFiles.wasm.gz": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.IO.MemoryMappedFiles.wasm.gz"}, "Patterns": null}, "System.IO.Pipes.AccessControl.wasm.gz": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.IO.Pipes.AccessControl.wasm.gz"}, "Patterns": null}, "System.IO.Pipes.wasm.gz": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.IO.Pipes.wasm.gz"}, "Patterns": null}, "System.IO.UnmanagedMemoryStream.wasm.gz": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.IO.UnmanagedMemoryStream.wasm.gz"}, "Patterns": null}, "System.IO.wasm.gz": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.IO.wasm.gz"}, "Patterns": null}, "System.Linq.Expressions.wasm.gz": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Linq.Expressions.wasm.gz"}, "Patterns": null}, "System.Linq.Parallel.wasm.gz": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Linq.Parallel.wasm.gz"}, "Patterns": null}, "System.Linq.Queryable.wasm.gz": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Linq.Queryable.wasm.gz"}, "Patterns": null}, "System.Linq.wasm.gz": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Linq.wasm.gz"}, "Patterns": null}, "System.Memory.wasm.gz": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Memory.wasm.gz"}, "Patterns": null}, "System.Net.Http.Json.wasm.gz": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Net.Http.Json.wasm.gz"}, "Patterns": null}, "System.Net.Http.wasm.gz": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Net.Http.wasm.gz"}, "Patterns": null}, "System.Net.HttpListener.wasm.gz": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Net.HttpListener.wasm.gz"}, "Patterns": null}, "System.Net.Mail.wasm.gz": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Net.Mail.wasm.gz"}, "Patterns": null}, "System.Net.NameResolution.wasm.gz": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Net.NameResolution.wasm.gz"}, "Patterns": null}, "System.Net.NetworkInformation.wasm.gz": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Net.NetworkInformation.wasm.gz"}, "Patterns": null}, "System.Net.Ping.wasm.gz": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Net.Ping.wasm.gz"}, "Patterns": null}, "System.Net.Primitives.wasm.gz": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Net.Primitives.wasm.gz"}, "Patterns": null}, "System.Net.Quic.wasm.gz": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Net.Quic.wasm.gz"}, "Patterns": null}, "System.Net.Requests.wasm.gz": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Net.Requests.wasm.gz"}, "Patterns": null}, "System.Net.Security.wasm.gz": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Net.Security.wasm.gz"}, "Patterns": null}, "System.Net.ServicePoint.wasm.gz": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Net.ServicePoint.wasm.gz"}, "Patterns": null}, "System.Net.Sockets.wasm.gz": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Net.Sockets.wasm.gz"}, "Patterns": null}, "System.Net.WebClient.wasm.gz": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Net.WebClient.wasm.gz"}, "Patterns": null}, "System.Net.WebHeaderCollection.wasm.gz": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Net.WebHeaderCollection.wasm.gz"}, "Patterns": null}, "System.Net.WebProxy.wasm.gz": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Net.WebProxy.wasm.gz"}, "Patterns": null}, "System.Net.WebSockets.Client.wasm.gz": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Net.WebSockets.Client.wasm.gz"}, "Patterns": null}, "System.Net.WebSockets.wasm.gz": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Net.WebSockets.wasm.gz"}, "Patterns": null}, "System.Net.wasm.gz": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Net.wasm.gz"}, "Patterns": null}, "System.Numerics.Vectors.wasm.gz": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Numerics.Vectors.wasm.gz"}, "Patterns": null}, "System.Numerics.wasm.gz": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Numerics.wasm.gz"}, "Patterns": null}, "System.ObjectModel.wasm.gz": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.ObjectModel.wasm.gz"}, "Patterns": null}, "System.Private.DataContractSerialization.wasm.gz": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Private.DataContractSerialization.wasm.gz"}, "Patterns": null}, "System.Private.Uri.wasm.gz": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Private.Uri.wasm.gz"}, "Patterns": null}, "System.Private.Xml.Linq.wasm.gz": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Private.Xml.Linq.wasm.gz"}, "Patterns": null}, "System.Private.Xml.wasm.gz": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Private.Xml.wasm.gz"}, "Patterns": null}, "System.Reflection.DispatchProxy.wasm.gz": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Reflection.DispatchProxy.wasm.gz"}, "Patterns": null}, "System.Reflection.Emit.ILGeneration.wasm.gz": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Reflection.Emit.ILGeneration.wasm.gz"}, "Patterns": null}, "System.Reflection.Emit.Lightweight.wasm.gz": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Reflection.Emit.Lightweight.wasm.gz"}, "Patterns": null}, "System.Reflection.Emit.wasm.gz": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Reflection.Emit.wasm.gz"}, "Patterns": null}, "System.Reflection.Extensions.wasm.gz": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Reflection.Extensions.wasm.gz"}, "Patterns": null}, "System.Reflection.Metadata.wasm.gz": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Reflection.Metadata.wasm.gz"}, "Patterns": null}, "System.Reflection.Primitives.wasm.gz": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Reflection.Primitives.wasm.gz"}, "Patterns": null}, "System.Reflection.TypeExtensions.wasm.gz": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Reflection.TypeExtensions.wasm.gz"}, "Patterns": null}, "System.Reflection.wasm.gz": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Reflection.wasm.gz"}, "Patterns": null}, "System.Resources.Reader.wasm.gz": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Resources.Reader.wasm.gz"}, "Patterns": null}, "System.Resources.ResourceManager.wasm.gz": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Resources.ResourceManager.wasm.gz"}, "Patterns": null}, "System.Resources.Writer.wasm.gz": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Resources.Writer.wasm.gz"}, "Patterns": null}, "System.Runtime.CompilerServices.Unsafe.wasm.gz": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Runtime.CompilerServices.Unsafe.wasm.gz"}, "Patterns": null}, "System.Runtime.CompilerServices.VisualC.wasm.gz": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Runtime.CompilerServices.VisualC.wasm.gz"}, "Patterns": null}, "System.Runtime.Extensions.wasm.gz": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Runtime.Extensions.wasm.gz"}, "Patterns": null}, "System.Runtime.Handles.wasm.gz": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Runtime.Handles.wasm.gz"}, "Patterns": null}, "System.Runtime.InteropServices.JavaScript.wasm.gz": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Runtime.InteropServices.JavaScript.wasm.gz"}, "Patterns": null}, "System.Runtime.InteropServices.RuntimeInformation.wasm.gz": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Runtime.InteropServices.RuntimeInformation.wasm.gz"}, "Patterns": null}, "System.Runtime.InteropServices.wasm.gz": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Runtime.InteropServices.wasm.gz"}, "Patterns": null}, "System.Runtime.Intrinsics.wasm.gz": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Runtime.Intrinsics.wasm.gz"}, "Patterns": null}, "System.Runtime.Loader.wasm.gz": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Runtime.Loader.wasm.gz"}, "Patterns": null}, "System.Runtime.Numerics.wasm.gz": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Runtime.Numerics.wasm.gz"}, "Patterns": null}, "System.Runtime.Serialization.Formatters.wasm.gz": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Runtime.Serialization.Formatters.wasm.gz"}, "Patterns": null}, "System.Runtime.Serialization.Json.wasm.gz": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Runtime.Serialization.Json.wasm.gz"}, "Patterns": null}, "System.Runtime.Serialization.Primitives.wasm.gz": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Runtime.Serialization.Primitives.wasm.gz"}, "Patterns": null}, "System.Runtime.Serialization.Xml.wasm.gz": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Runtime.Serialization.Xml.wasm.gz"}, "Patterns": null}, "System.Runtime.Serialization.wasm.gz": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Runtime.Serialization.wasm.gz"}, "Patterns": null}, "System.Runtime.wasm.gz": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Runtime.wasm.gz"}, "Patterns": null}, "System.Security.AccessControl.wasm.gz": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Security.AccessControl.wasm.gz"}, "Patterns": null}, "System.Security.Claims.wasm.gz": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Security.Claims.wasm.gz"}, "Patterns": null}, "System.Security.Cryptography.Algorithms.wasm.gz": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Security.Cryptography.Algorithms.wasm.gz"}, "Patterns": null}, "System.Security.Cryptography.Cng.wasm.gz": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Security.Cryptography.Cng.wasm.gz"}, "Patterns": null}, "System.Security.Cryptography.Csp.wasm.gz": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Security.Cryptography.Csp.wasm.gz"}, "Patterns": null}, "System.Security.Cryptography.Encoding.wasm.gz": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Security.Cryptography.Encoding.wasm.gz"}, "Patterns": null}, "System.Security.Cryptography.OpenSsl.wasm.gz": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Security.Cryptography.OpenSsl.wasm.gz"}, "Patterns": null}, "System.Security.Cryptography.Primitives.wasm.gz": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Security.Cryptography.Primitives.wasm.gz"}, "Patterns": null}, "System.Security.Cryptography.X509Certificates.wasm.gz": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Security.Cryptography.X509Certificates.wasm.gz"}, "Patterns": null}, "System.Security.Cryptography.wasm.gz": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Security.Cryptography.wasm.gz"}, "Patterns": null}, "System.Security.Principal.Windows.wasm.gz": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Security.Principal.Windows.wasm.gz"}, "Patterns": null}, "System.Security.Principal.wasm.gz": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Security.Principal.wasm.gz"}, "Patterns": null}, "System.Security.SecureString.wasm.gz": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Security.SecureString.wasm.gz"}, "Patterns": null}, "System.Security.wasm.gz": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Security.wasm.gz"}, "Patterns": null}, "System.ServiceModel.Web.wasm.gz": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.ServiceModel.Web.wasm.gz"}, "Patterns": null}, "System.ServiceProcess.wasm.gz": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.ServiceProcess.wasm.gz"}, "Patterns": null}, "System.Text.Encoding.CodePages.wasm.gz": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Text.Encoding.CodePages.wasm.gz"}, "Patterns": null}, "System.Text.Encoding.Extensions.wasm.gz": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Text.Encoding.Extensions.wasm.gz"}, "Patterns": null}, "System.Text.Encoding.wasm.gz": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Text.Encoding.wasm.gz"}, "Patterns": null}, "System.Text.Encodings.Web.wasm.gz": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Text.Encodings.Web.wasm.gz"}, "Patterns": null}, "System.Text.Json.wasm.gz": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Text.Json.wasm.gz"}, "Patterns": null}, "System.Text.RegularExpressions.wasm.gz": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Text.RegularExpressions.wasm.gz"}, "Patterns": null}, "System.Threading.Channels.wasm.gz": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Threading.Channels.wasm.gz"}, "Patterns": null}, "System.Threading.Overlapped.wasm.gz": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Threading.Overlapped.wasm.gz"}, "Patterns": null}, "System.Threading.Tasks.Dataflow.wasm.gz": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Threading.Tasks.Dataflow.wasm.gz"}, "Patterns": null}, "System.Threading.Tasks.Extensions.wasm.gz": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Threading.Tasks.Extensions.wasm.gz"}, "Patterns": null}, "System.Threading.Tasks.Parallel.wasm.gz": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Threading.Tasks.Parallel.wasm.gz"}, "Patterns": null}, "System.Threading.Tasks.wasm.gz": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Threading.Tasks.wasm.gz"}, "Patterns": null}, "System.Threading.Thread.wasm.gz": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Threading.Thread.wasm.gz"}, "Patterns": null}, "System.Threading.ThreadPool.wasm.gz": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Threading.ThreadPool.wasm.gz"}, "Patterns": null}, "System.Threading.Timer.wasm.gz": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Threading.Timer.wasm.gz"}, "Patterns": null}, "System.Threading.wasm.gz": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Threading.wasm.gz"}, "Patterns": null}, "System.Transactions.Local.wasm.gz": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Transactions.Local.wasm.gz"}, "Patterns": null}, "System.Transactions.wasm.gz": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Transactions.wasm.gz"}, "Patterns": null}, "System.ValueTuple.wasm.gz": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.ValueTuple.wasm.gz"}, "Patterns": null}, "System.Web.HttpUtility.wasm.gz": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Web.HttpUtility.wasm.gz"}, "Patterns": null}, "System.Web.wasm.gz": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Web.wasm.gz"}, "Patterns": null}, "System.Windows.wasm.gz": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Windows.wasm.gz"}, "Patterns": null}, "System.Xml.Linq.wasm.gz": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Xml.Linq.wasm.gz"}, "Patterns": null}, "System.Xml.ReaderWriter.wasm.gz": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Xml.ReaderWriter.wasm.gz"}, "Patterns": null}, "System.Xml.Serialization.wasm.gz": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Xml.Serialization.wasm.gz"}, "Patterns": null}, "System.Xml.XDocument.wasm.gz": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Xml.XDocument.wasm.gz"}, "Patterns": null}, "System.Xml.XPath.XDocument.wasm.gz": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Xml.XPath.XDocument.wasm.gz"}, "Patterns": null}, "System.Xml.XPath.wasm.gz": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Xml.XPath.wasm.gz"}, "Patterns": null}, "System.Xml.XmlDocument.wasm.gz": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Xml.XmlDocument.wasm.gz"}, "Patterns": null}, "System.Xml.XmlSerializer.wasm.gz": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Xml.XmlSerializer.wasm.gz"}, "Patterns": null}, "System.Xml.wasm.gz": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Xml.wasm.gz"}, "Patterns": null}, "System.wasm.gz": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.wasm.gz"}, "Patterns": null}, "WindowsBase.wasm.gz": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/WindowsBase.wasm.gz"}, "Patterns": null}, "mscorlib.wasm.gz": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/mscorlib.wasm.gz"}, "Patterns": null}, "netstandard.wasm.gz": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/netstandard.wasm.gz"}, "Patterns": null}, "System.Private.CoreLib.wasm.gz": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/System.Private.CoreLib.wasm.gz"}, "Patterns": null}, "dotnet.js.gz": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/dotnet.js.gz"}, "Patterns": null}, "dotnet.js.map.gz": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/dotnet.js.map.gz"}, "Patterns": null}, "dotnet.native.8.0.20.frvb3disbh.js.gz": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/dotnet.native.8.0.20.frvb3disbh.js.gz"}, "Patterns": null}, "dotnet.native.wasm.gz": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/dotnet.native.wasm.gz"}, "Patterns": null}, "dotnet.runtime.8.0.20.gped7iqcy4.js.gz": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/dotnet.runtime.8.0.20.gped7iqcy4.js.gz"}, "Patterns": null}, "dotnet.runtime.js.map.gz": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/dotnet.runtime.js.map.gz"}, "Patterns": null}, "icudt_CJK.dat.gz": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/icudt_CJK.dat.gz"}, "Patterns": null}, "icudt_EFIGS.dat.gz": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/icudt_EFIGS.dat.gz"}, "Patterns": null}, "icudt_no_CJK.dat.gz": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/icudt_no_CJK.dat.gz"}, "Patterns": null}, "UserManagement.Shared.wasm.gz": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/UserManagement.Shared.wasm.gz"}, "Patterns": null}, "UserManagement.Shared.pdb.gz": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/UserManagement.Shared.pdb.gz"}, "Patterns": null}, "UserManagement.Client.wasm.gz": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/UserManagement.Client.wasm.gz"}, "Patterns": null}, "UserManagement.Client.pdb.gz": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/UserManagement.Client.pdb.gz"}, "Patterns": null}, "blazor.boot.json.gz": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "_framework/blazor.boot.json.gz"}, "Patterns": null}}, "Asset": null, "Patterns": null}, "UserManagement.Client.styles.css": {"Children": null, "Asset": {"ContentRootIndex": 2, "SubPath": "UserManagement.Client.styles.css"}, "Patterns": null}}, "Asset": null, "Patterns": [{"ContentRootIndex": 0, "Pattern": "**", "Depth": 0}]}}