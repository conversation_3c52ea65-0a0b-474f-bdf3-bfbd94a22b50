{"Files": [{"Id": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/bin/Debug/net8.0/wwwroot/_framework/blazor.webassembly.js", "PackagePath": "staticwebassets/_framework/blazor.webassembly.js"}, {"Id": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/compressed/mh6vcvd7vn.gz", "PackagePath": "staticwebassets/_framework/blazor.webassembly.js.gz"}, {"Id": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/Debug/net8.0/scopedcss/bundle/UserManagement.Client.styles.css", "PackagePath": "staticwebassets/UserManagement.Client.styles.css"}, {"Id": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/wwwroot/css/app.css", "PackagePath": "staticwebassets/css/app.css"}, {"Id": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/wwwroot/css/bootstrap/bootstrap.min.css", "PackagePath": "staticwebassets/css/bootstrap/bootstrap.min.css"}, {"Id": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/wwwroot/css/bootstrap/bootstrap.min.css.map", "PackagePath": "staticwebassets/css/bootstrap/bootstrap.min.css.map"}, {"Id": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/wwwroot/favicon.png", "PackagePath": "staticwebassets/favicon.png"}, {"Id": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/wwwroot/icon-192.png", "PackagePath": "staticwebassets/icon-192.png"}, {"Id": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/wwwroot/index.html", "PackagePath": "staticwebassets/index.html"}, {"Id": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/wwwroot/sample-data/weather.json", "PackagePath": "staticwebassets/sample-data/weather.json"}, {"Id": "obj/Debug/net8.0/staticwebassets/msbuild.UserManagement.Client.Microsoft.AspNetCore.StaticWebAssets.props", "PackagePath": "build\\Microsoft.AspNetCore.StaticWebAssets.props"}, {"Id": "obj/Debug/net8.0/staticwebassets/msbuild.build.UserManagement.Client.props", "PackagePath": "build\\UserManagement.Client.props"}, {"Id": "obj/Debug/net8.0/staticwebassets/msbuild.buildMultiTargeting.UserManagement.Client.props", "PackagePath": "buildMultiTargeting\\UserManagement.Client.props"}, {"Id": "obj/Debug/net8.0/staticwebassets/msbuild.buildTransitive.UserManagement.Client.props", "PackagePath": "buildTransitive\\UserManagement.Client.props"}], "ElementsToRemove": []}