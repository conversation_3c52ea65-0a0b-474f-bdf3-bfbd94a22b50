{"version": 3, "targets": {"net8.0": {"Microsoft.AspNetCore.Authorization/8.0.20": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Metadata": "8.0.20", "Microsoft.Extensions.Logging.Abstractions": "8.0.3", "Microsoft.Extensions.Options": "8.0.2"}, "compile": {"lib/net8.0/Microsoft.AspNetCore.Authorization.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.AspNetCore.Authorization.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.Components/8.0.20": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Authorization": "8.0.20", "Microsoft.AspNetCore.Components.Analyzers": "8.0.20"}, "compile": {"lib/net8.0/Microsoft.AspNetCore.Components.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.AspNetCore.Components.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.Components.Analyzers/8.0.20": {"type": "package", "build": {"buildTransitive/netstandard2.0/Microsoft.AspNetCore.Components.Analyzers.targets": {}}}, "Microsoft.AspNetCore.Components.Forms/8.0.20": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Components": "8.0.20"}, "compile": {"lib/net8.0/Microsoft.AspNetCore.Components.Forms.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.AspNetCore.Components.Forms.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.Components.Web/8.0.20": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Components": "8.0.20", "Microsoft.AspNetCore.Components.Forms": "8.0.20", "Microsoft.Extensions.DependencyInjection": "8.0.1", "Microsoft.Extensions.Primitives": "8.0.0", "Microsoft.JSInterop": "8.0.20", "System.IO.Pipelines": "8.0.0"}, "compile": {"lib/net8.0/Microsoft.AspNetCore.Components.Web.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.AspNetCore.Components.Web.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.Components.WebAssembly/8.0.20": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Components.Web": "8.0.20", "Microsoft.Extensions.Configuration.Binder": "8.0.2", "Microsoft.Extensions.Configuration.Json": "8.0.1", "Microsoft.Extensions.Logging": "8.0.1", "Microsoft.JSInterop.WebAssembly": "8.0.20"}, "compile": {"lib/net8.0/Microsoft.AspNetCore.Components.WebAssembly.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.AspNetCore.Components.WebAssembly.dll": {"related": ".xml"}}, "build": {"build/net8.0/Microsoft.AspNetCore.Components.WebAssembly.props": {}}}, "Microsoft.AspNetCore.Components.WebAssembly.DevServer/8.0.20": {"type": "package", "build": {"build/Microsoft.AspNetCore.Components.WebAssembly.DevServer.targets": {}}}, "Microsoft.AspNetCore.Metadata/8.0.20": {"type": "package", "compile": {"lib/net8.0/Microsoft.AspNetCore.Metadata.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.AspNetCore.Metadata.dll": {"related": ".xml"}}}, "Microsoft.Extensions.Configuration/8.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0"}, "compile": {"lib/net8.0/Microsoft.Extensions.Configuration.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.Configuration.Abstractions/8.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Primitives": "8.0.0"}, "compile": {"lib/net8.0/Microsoft.Extensions.Configuration.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.Configuration.Binder/8.0.2": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0"}, "compile": {"lib/net8.0/Microsoft.Extensions.Configuration.Binder.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.Binder.dll": {"related": ".xml"}}, "build": {"buildTransitive/netstandard2.0/Microsoft.Extensions.Configuration.Binder.targets": {}}}, "Microsoft.Extensions.Configuration.FileExtensions/8.0.1": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "8.0.0", "Microsoft.Extensions.FileProviders.Physical": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0"}, "compile": {"lib/net8.0/Microsoft.Extensions.Configuration.FileExtensions.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.FileExtensions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.Configuration.Json/8.0.1": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.Configuration.FileExtensions": "8.0.1", "Microsoft.Extensions.FileProviders.Abstractions": "8.0.0"}, "compile": {"lib/net8.0/Microsoft.Extensions.Configuration.Json.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.Json.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.DependencyInjection/8.0.1": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.2"}, "compile": {"lib/net8.0/Microsoft.Extensions.DependencyInjection.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.DependencyInjection.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/8.0.2": {"type": "package", "compile": {"lib/net8.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.FileProviders.Abstractions/8.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Primitives": "8.0.0"}, "compile": {"lib/net8.0/Microsoft.Extensions.FileProviders.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.FileProviders.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.FileProviders.Physical/8.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.FileProviders.Abstractions": "8.0.0", "Microsoft.Extensions.FileSystemGlobbing": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0"}, "compile": {"lib/net8.0/Microsoft.Extensions.FileProviders.Physical.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.FileProviders.Physical.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.FileSystemGlobbing/8.0.0": {"type": "package", "compile": {"lib/net8.0/Microsoft.Extensions.FileSystemGlobbing.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.FileSystemGlobbing.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.Logging/8.0.1": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection": "8.0.1", "Microsoft.Extensions.Logging.Abstractions": "8.0.2", "Microsoft.Extensions.Options": "8.0.2"}, "compile": {"lib/net8.0/Microsoft.Extensions.Logging.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Logging.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.Logging.Abstractions/8.0.3": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.2"}, "compile": {"lib/net8.0/Microsoft.Extensions.Logging.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Logging.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/Microsoft.Extensions.Logging.Abstractions.targets": {}}}, "Microsoft.Extensions.Options/8.0.2": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0"}, "compile": {"lib/net8.0/Microsoft.Extensions.Options.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Options.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/Microsoft.Extensions.Options.targets": {}}}, "Microsoft.Extensions.Primitives/8.0.0": {"type": "package", "compile": {"lib/net8.0/Microsoft.Extensions.Primitives.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Primitives.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.JSInterop/8.0.20": {"type": "package", "compile": {"lib/net8.0/Microsoft.JSInterop.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.JSInterop.dll": {"related": ".xml"}}}, "Microsoft.JSInterop.WebAssembly/8.0.20": {"type": "package", "dependencies": {"Microsoft.JSInterop": "8.0.20"}, "compile": {"lib/net8.0/Microsoft.JSInterop.WebAssembly.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.JSInterop.WebAssembly.dll": {"related": ".xml"}}}, "Microsoft.NET.ILLink.Tasks/8.0.20": {"type": "package", "build": {"build/Microsoft.NET.ILLink.Tasks.props": {}}}, "Microsoft.NET.Sdk.WebAssembly.Pack/8.0.20": {"type": "package", "build": {"build/Microsoft.NET.Sdk.WebAssembly.Pack.props": {}, "build/Microsoft.NET.Sdk.WebAssembly.Pack.targets": {}}}, "System.IO.Pipelines/8.0.0": {"type": "package", "compile": {"lib/net8.0/System.IO.Pipelines.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/System.IO.Pipelines.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "UserManagement.Shared/1.0.0": {"type": "project", "framework": ".NETCoreApp,Version=v8.0", "compile": {"bin/placeholder/UserManagement.Shared.dll": {}}, "runtime": {"bin/placeholder/UserManagement.Shared.dll": {}}}}, "net8.0/browser-wasm": {"Microsoft.AspNetCore.Authorization/8.0.20": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Metadata": "8.0.20", "Microsoft.Extensions.Logging.Abstractions": "8.0.3", "Microsoft.Extensions.Options": "8.0.2"}, "compile": {"lib/net8.0/Microsoft.AspNetCore.Authorization.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.AspNetCore.Authorization.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.Components/8.0.20": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Authorization": "8.0.20", "Microsoft.AspNetCore.Components.Analyzers": "8.0.20"}, "compile": {"lib/net8.0/Microsoft.AspNetCore.Components.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.AspNetCore.Components.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.Components.Analyzers/8.0.20": {"type": "package", "build": {"buildTransitive/netstandard2.0/Microsoft.AspNetCore.Components.Analyzers.targets": {}}}, "Microsoft.AspNetCore.Components.Forms/8.0.20": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Components": "8.0.20"}, "compile": {"lib/net8.0/Microsoft.AspNetCore.Components.Forms.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.AspNetCore.Components.Forms.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.Components.Web/8.0.20": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Components": "8.0.20", "Microsoft.AspNetCore.Components.Forms": "8.0.20", "Microsoft.Extensions.DependencyInjection": "8.0.1", "Microsoft.Extensions.Primitives": "8.0.0", "Microsoft.JSInterop": "8.0.20", "System.IO.Pipelines": "8.0.0"}, "compile": {"lib/net8.0/Microsoft.AspNetCore.Components.Web.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.AspNetCore.Components.Web.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.Components.WebAssembly/8.0.20": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Components.Web": "8.0.20", "Microsoft.Extensions.Configuration.Binder": "8.0.2", "Microsoft.Extensions.Configuration.Json": "8.0.1", "Microsoft.Extensions.Logging": "8.0.1", "Microsoft.JSInterop.WebAssembly": "8.0.20"}, "compile": {"lib/net8.0/Microsoft.AspNetCore.Components.WebAssembly.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.AspNetCore.Components.WebAssembly.dll": {"related": ".xml"}}, "build": {"build/net8.0/Microsoft.AspNetCore.Components.WebAssembly.props": {}}}, "Microsoft.AspNetCore.Components.WebAssembly.DevServer/8.0.20": {"type": "package", "build": {"build/Microsoft.AspNetCore.Components.WebAssembly.DevServer.targets": {}}}, "Microsoft.AspNetCore.Metadata/8.0.20": {"type": "package", "compile": {"lib/net8.0/Microsoft.AspNetCore.Metadata.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.AspNetCore.Metadata.dll": {"related": ".xml"}}}, "Microsoft.Extensions.Configuration/8.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0"}, "compile": {"lib/net8.0/Microsoft.Extensions.Configuration.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.Configuration.Abstractions/8.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Primitives": "8.0.0"}, "compile": {"lib/net8.0/Microsoft.Extensions.Configuration.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.Configuration.Binder/8.0.2": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0"}, "compile": {"lib/net8.0/Microsoft.Extensions.Configuration.Binder.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.Binder.dll": {"related": ".xml"}}, "build": {"buildTransitive/netstandard2.0/Microsoft.Extensions.Configuration.Binder.targets": {}}}, "Microsoft.Extensions.Configuration.FileExtensions/8.0.1": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "8.0.0", "Microsoft.Extensions.FileProviders.Physical": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0"}, "compile": {"lib/net8.0/Microsoft.Extensions.Configuration.FileExtensions.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.FileExtensions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.Configuration.Json/8.0.1": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.Configuration.FileExtensions": "8.0.1", "Microsoft.Extensions.FileProviders.Abstractions": "8.0.0"}, "compile": {"lib/net8.0/Microsoft.Extensions.Configuration.Json.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.Json.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.DependencyInjection/8.0.1": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.2"}, "compile": {"lib/net8.0/Microsoft.Extensions.DependencyInjection.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.DependencyInjection.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/8.0.2": {"type": "package", "compile": {"lib/net8.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.FileProviders.Abstractions/8.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Primitives": "8.0.0"}, "compile": {"lib/net8.0/Microsoft.Extensions.FileProviders.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.FileProviders.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.FileProviders.Physical/8.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.FileProviders.Abstractions": "8.0.0", "Microsoft.Extensions.FileSystemGlobbing": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0"}, "compile": {"lib/net8.0/Microsoft.Extensions.FileProviders.Physical.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.FileProviders.Physical.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.FileSystemGlobbing/8.0.0": {"type": "package", "compile": {"lib/net8.0/Microsoft.Extensions.FileSystemGlobbing.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.FileSystemGlobbing.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.Logging/8.0.1": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection": "8.0.1", "Microsoft.Extensions.Logging.Abstractions": "8.0.2", "Microsoft.Extensions.Options": "8.0.2"}, "compile": {"lib/net8.0/Microsoft.Extensions.Logging.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Logging.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.Logging.Abstractions/8.0.3": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.2"}, "compile": {"lib/net8.0/Microsoft.Extensions.Logging.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Logging.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/Microsoft.Extensions.Logging.Abstractions.targets": {}}}, "Microsoft.Extensions.Options/8.0.2": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0"}, "compile": {"lib/net8.0/Microsoft.Extensions.Options.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Options.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/Microsoft.Extensions.Options.targets": {}}}, "Microsoft.Extensions.Primitives/8.0.0": {"type": "package", "compile": {"lib/net8.0/Microsoft.Extensions.Primitives.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Primitives.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.JSInterop/8.0.20": {"type": "package", "compile": {"lib/net8.0/Microsoft.JSInterop.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.JSInterop.dll": {"related": ".xml"}}}, "Microsoft.JSInterop.WebAssembly/8.0.20": {"type": "package", "dependencies": {"Microsoft.JSInterop": "8.0.20"}, "compile": {"lib/net8.0/Microsoft.JSInterop.WebAssembly.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.JSInterop.WebAssembly.dll": {"related": ".xml"}}}, "Microsoft.NET.ILLink.Tasks/8.0.20": {"type": "package", "build": {"build/Microsoft.NET.ILLink.Tasks.props": {}}}, "Microsoft.NET.Sdk.WebAssembly.Pack/8.0.20": {"type": "package", "build": {"build/Microsoft.NET.Sdk.WebAssembly.Pack.props": {}, "build/Microsoft.NET.Sdk.WebAssembly.Pack.targets": {}}}, "System.IO.Pipelines/8.0.0": {"type": "package", "compile": {"lib/net8.0/System.IO.Pipelines.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/System.IO.Pipelines.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "UserManagement.Shared/1.0.0": {"type": "project", "framework": ".NETCoreApp,Version=v8.0", "compile": {"bin/placeholder/UserManagement.Shared.dll": {}}, "runtime": {"bin/placeholder/UserManagement.Shared.dll": {}}}}}, "libraries": {"Microsoft.AspNetCore.Authorization/8.0.20": {"sha512": "295bKuC0id+di1sGf/flQHLvnlwD+9yuvd43Eq2ITEMtqfb5SxvcVA0xcXvNo/Zd3uS/VRXQGJ3CHIMlPgDwtw==", "type": "package", "path": "microsoft.aspnetcore.authorization/8.0.20", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "THIRD-PARTY-NOTICES.TXT", "lib/net462/Microsoft.AspNetCore.Authorization.dll", "lib/net462/Microsoft.AspNetCore.Authorization.xml", "lib/net8.0/Microsoft.AspNetCore.Authorization.dll", "lib/net8.0/Microsoft.AspNetCore.Authorization.xml", "lib/netstandard2.0/Microsoft.AspNetCore.Authorization.dll", "lib/netstandard2.0/Microsoft.AspNetCore.Authorization.xml", "microsoft.aspnetcore.authorization.8.0.20.nupkg.sha512", "microsoft.aspnetcore.authorization.nuspec"]}, "Microsoft.AspNetCore.Components/8.0.20": {"sha512": "ZSgRdT6bUeq2h4jR8g3nGMy/k8e/9uQAIok8YNesgn+MiTt6szdNLCyBwKyvkUOkxtPFSiquN8Lq43WUHF86xw==", "type": "package", "path": "microsoft.aspnetcore.components/8.0.20", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "THIRD-PARTY-NOTICES.txt", "lib/net8.0/Microsoft.AspNetCore.Components.dll", "lib/net8.0/Microsoft.AspNetCore.Components.xml", "microsoft.aspnetcore.components.8.0.20.nupkg.sha512", "microsoft.aspnetcore.components.nuspec"]}, "Microsoft.AspNetCore.Components.Analyzers/8.0.20": {"sha512": "n3GLOh3bLTQqxs2gDXXg+6QFF9rMtR51mS0lXbe0YtqxXtWEfj+Jdm+SOuZV2l3kZGuxxjzo6MU/LwmNA4dngw==", "type": "package", "path": "microsoft.aspnetcore.components.analyzers/8.0.20", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "THIRD-PARTY-NOTICES.txt", "analyzers/dotnet/cs/Microsoft.AspNetCore.Components.Analyzers.dll", "build/netstandard2.0/Microsoft.AspNetCore.Components.Analyzers.targets", "buildTransitive/netstandard2.0/Microsoft.AspNetCore.Components.Analyzers.targets", "microsoft.aspnetcore.components.analyzers.8.0.20.nupkg.sha512", "microsoft.aspnetcore.components.analyzers.nuspec"]}, "Microsoft.AspNetCore.Components.Forms/8.0.20": {"sha512": "b2Xj7WgiNFHxVW9lk59T9Q4xkKg1JJwgHc3O/drzfDxYs9aMOY7TRnc4rqb0a56u+4daHcEym806gO2MSQRFDg==", "type": "package", "path": "microsoft.aspnetcore.components.forms/8.0.20", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "THIRD-PARTY-NOTICES.txt", "lib/net8.0/Microsoft.AspNetCore.Components.Forms.dll", "lib/net8.0/Microsoft.AspNetCore.Components.Forms.xml", "microsoft.aspnetcore.components.forms.8.0.20.nupkg.sha512", "microsoft.aspnetcore.components.forms.nuspec"]}, "Microsoft.AspNetCore.Components.Web/8.0.20": {"sha512": "I4gboA+JSe1cJgohZvsZYz8uZ2LAh0d68A7tY7If8h+H+QJg3aEQJd0HDtF/cD1C9QzLZCDhD0lXbi+DSvTcGA==", "type": "package", "path": "microsoft.aspnetcore.components.web/8.0.20", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "THIRD-PARTY-NOTICES.txt", "lib/net8.0/Microsoft.AspNetCore.Components.Web.dll", "lib/net8.0/Microsoft.AspNetCore.Components.Web.xml", "microsoft.aspnetcore.components.web.8.0.20.nupkg.sha512", "microsoft.aspnetcore.components.web.nuspec"]}, "Microsoft.AspNetCore.Components.WebAssembly/8.0.20": {"sha512": "57JM6RaOVkZgE6l+B7xzyVcHuX9N1331NBuSqTLAU/k6EMV50SZiP/h6gKO/Ck5O+CScI7sHz6XTWhzXAaqmvg==", "type": "package", "path": "microsoft.aspnetcore.components.webassembly/8.0.20", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "THIRD-PARTY-NOTICES.txt", "build/net8.0/Microsoft.AspNetCore.Components.WebAssembly.props", "build/net8.0/blazor.webassembly.js", "lib/net8.0/Microsoft.AspNetCore.Components.WebAssembly.dll", "lib/net8.0/Microsoft.AspNetCore.Components.WebAssembly.xml", "microsoft.aspnetcore.components.webassembly.8.0.20.nupkg.sha512", "microsoft.aspnetcore.components.webassembly.nuspec"]}, "Microsoft.AspNetCore.Components.WebAssembly.DevServer/8.0.20": {"sha512": "LDozfCFr9GQGUhgtSxW0ViGwxco+NuxLu2vvn/fgCOYt40LvyCoNNRMeGY99PP2X82N1X0gF0RSR+Xen6UA19A==", "type": "package", "path": "microsoft.aspnetcore.components.webassembly.devserver/8.0.20", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "THIRD-PARTY-NOTICES.txt", "build/Microsoft.AspNetCore.Components.WebAssembly.DevServer.targets", "microsoft.aspnetcore.components.webassembly.devserver.8.0.20.nupkg.sha512", "microsoft.aspnetcore.components.webassembly.devserver.nuspec", "tools/ARM64/aspnetcorev2_inprocess.dll", "tools/BlazorDebugProxy/BrowserDebugHost.dll", "tools/BlazorDebugProxy/BrowserDebugHost.runtimeconfig.json", "tools/BlazorDebugProxy/BrowserDebugProxy.dll", "tools/BlazorDebugProxy/Microsoft.CodeAnalysis.CSharp.Scripting.dll", "tools/BlazorDebugProxy/Microsoft.CodeAnalysis.CSharp.dll", "tools/BlazorDebugProxy/Microsoft.CodeAnalysis.Scripting.dll", "tools/BlazorDebugProxy/Microsoft.CodeAnalysis.dll", "tools/BlazorDebugProxy/Microsoft.FileFormats.dll", "tools/BlazorDebugProxy/Microsoft.NET.WebAssembly.Webcil.dll", "tools/BlazorDebugProxy/Microsoft.SymbolStore.dll", "tools/BlazorDebugProxy/Newtonsoft.Json.dll", "tools/Microsoft.AspNetCore.Antiforgery.dll", "tools/Microsoft.AspNetCore.Antiforgery.xml", "tools/Microsoft.AspNetCore.Authentication.Abstractions.dll", "tools/Microsoft.AspNetCore.Authentication.Abstractions.xml", "tools/Microsoft.AspNetCore.Authentication.Core.dll", "tools/Microsoft.AspNetCore.Authentication.Core.xml", "tools/Microsoft.AspNetCore.Authentication.dll", "tools/Microsoft.AspNetCore.Authentication.xml", "tools/Microsoft.AspNetCore.Authorization.Policy.dll", "tools/Microsoft.AspNetCore.Authorization.Policy.xml", "tools/Microsoft.AspNetCore.Authorization.dll", "tools/Microsoft.AspNetCore.Authorization.xml", "tools/Microsoft.AspNetCore.Components.Authorization.dll", "tools/Microsoft.AspNetCore.Components.Authorization.xml", "tools/Microsoft.AspNetCore.Components.Endpoints.dll", "tools/Microsoft.AspNetCore.Components.Endpoints.xml", "tools/Microsoft.AspNetCore.Components.Forms.dll", "tools/Microsoft.AspNetCore.Components.Forms.xml", "tools/Microsoft.AspNetCore.Components.Web.dll", "tools/Microsoft.AspNetCore.Components.Web.xml", "tools/Microsoft.AspNetCore.Components.WebAssembly.Server.dll", "tools/Microsoft.AspNetCore.Components.WebAssembly.Server.xml", "tools/Microsoft.AspNetCore.Components.dll", "tools/Microsoft.AspNetCore.Components.xml", "tools/Microsoft.AspNetCore.Connections.Abstractions.dll", "tools/Microsoft.AspNetCore.Connections.Abstractions.xml", "tools/Microsoft.AspNetCore.Cryptography.Internal.dll", "tools/Microsoft.AspNetCore.Cryptography.Internal.xml", "tools/Microsoft.AspNetCore.DataProtection.Abstractions.dll", "tools/Microsoft.AspNetCore.DataProtection.Abstractions.xml", "tools/Microsoft.AspNetCore.DataProtection.Extensions.dll", "tools/Microsoft.AspNetCore.DataProtection.Extensions.xml", "tools/Microsoft.AspNetCore.DataProtection.dll", "tools/Microsoft.AspNetCore.DataProtection.xml", "tools/Microsoft.AspNetCore.Diagnostics.Abstractions.dll", "tools/Microsoft.AspNetCore.Diagnostics.Abstractions.xml", "tools/Microsoft.AspNetCore.Diagnostics.dll", "tools/Microsoft.AspNetCore.Diagnostics.xml", "tools/Microsoft.AspNetCore.HostFiltering.dll", "tools/Microsoft.AspNetCore.HostFiltering.xml", "tools/Microsoft.AspNetCore.Hosting.Abstractions.dll", "tools/Microsoft.AspNetCore.Hosting.Abstractions.xml", "tools/Microsoft.AspNetCore.Hosting.Server.Abstractions.dll", "tools/Microsoft.AspNetCore.Hosting.Server.Abstractions.xml", "tools/Microsoft.AspNetCore.Hosting.dll", "tools/Microsoft.AspNetCore.Hosting.xml", "tools/Microsoft.AspNetCore.Html.Abstractions.dll", "tools/Microsoft.AspNetCore.Html.Abstractions.xml", "tools/Microsoft.AspNetCore.Http.Abstractions.dll", "tools/Microsoft.AspNetCore.Http.Abstractions.xml", "tools/Microsoft.AspNetCore.Http.Extensions.dll", "tools/Microsoft.AspNetCore.Http.Extensions.xml", "tools/Microsoft.AspNetCore.Http.Features.dll", "tools/Microsoft.AspNetCore.Http.Features.xml", "tools/Microsoft.AspNetCore.Http.dll", "tools/Microsoft.AspNetCore.Http.xml", "tools/Microsoft.AspNetCore.HttpOverrides.dll", "tools/Microsoft.AspNetCore.HttpOverrides.xml", "tools/Microsoft.AspNetCore.Metadata.dll", "tools/Microsoft.AspNetCore.Metadata.xml", "tools/Microsoft.AspNetCore.Routing.Abstractions.dll", "tools/Microsoft.AspNetCore.Routing.Abstractions.xml", "tools/Microsoft.AspNetCore.Routing.dll", "tools/Microsoft.AspNetCore.Routing.xml", "tools/Microsoft.AspNetCore.Server.HttpSys.dll", "tools/Microsoft.AspNetCore.Server.HttpSys.xml", "tools/Microsoft.AspNetCore.Server.IIS.dll", "tools/Microsoft.AspNetCore.Server.IIS.xml", "tools/Microsoft.AspNetCore.Server.IISIntegration.dll", "tools/Microsoft.AspNetCore.Server.IISIntegration.xml", "tools/Microsoft.AspNetCore.Server.Kestrel.Core.dll", "tools/Microsoft.AspNetCore.Server.Kestrel.Core.xml", "tools/Microsoft.AspNetCore.Server.Kestrel.Transport.NamedPipes.dll", "tools/Microsoft.AspNetCore.Server.Kestrel.Transport.NamedPipes.xml", "tools/Microsoft.AspNetCore.Server.Kestrel.Transport.Quic.dll", "tools/Microsoft.AspNetCore.Server.Kestrel.Transport.Quic.xml", "tools/Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.dll", "tools/Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.xml", "tools/Microsoft.AspNetCore.Server.Kestrel.dll", "tools/Microsoft.AspNetCore.Server.Kestrel.xml", "tools/Microsoft.AspNetCore.StaticFiles.dll", "tools/Microsoft.AspNetCore.StaticFiles.xml", "tools/Microsoft.AspNetCore.WebUtilities.dll", "tools/Microsoft.AspNetCore.WebUtilities.xml", "tools/Microsoft.AspNetCore.dll", "tools/Microsoft.AspNetCore.xml", "tools/Microsoft.Extensions.Configuration.Abstractions.dll", "tools/Microsoft.Extensions.Configuration.Binder.dll", "tools/Microsoft.Extensions.Configuration.CommandLine.dll", "tools/Microsoft.Extensions.Configuration.EnvironmentVariables.dll", "tools/Microsoft.Extensions.Configuration.FileExtensions.dll", "tools/Microsoft.Extensions.Configuration.Json.dll", "tools/Microsoft.Extensions.Configuration.UserSecrets.dll", "tools/Microsoft.Extensions.Configuration.dll", "tools/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "tools/Microsoft.Extensions.DependencyInjection.dll", "tools/Microsoft.Extensions.Diagnostics.Abstractions.dll", "tools/Microsoft.Extensions.Diagnostics.dll", "tools/Microsoft.Extensions.Features.dll", "tools/Microsoft.Extensions.Features.xml", "tools/Microsoft.Extensions.FileProviders.Abstractions.dll", "tools/Microsoft.Extensions.FileProviders.Composite.dll", "tools/Microsoft.Extensions.FileProviders.Embedded.dll", "tools/Microsoft.Extensions.FileProviders.Embedded.xml", "tools/Microsoft.Extensions.FileProviders.Physical.dll", "tools/Microsoft.Extensions.FileSystemGlobbing.dll", "tools/Microsoft.Extensions.Hosting.Abstractions.dll", "tools/Microsoft.Extensions.Hosting.dll", "tools/Microsoft.Extensions.Logging.Abstractions.dll", "tools/Microsoft.Extensions.Logging.Configuration.dll", "tools/Microsoft.Extensions.Logging.Console.dll", "tools/Microsoft.Extensions.Logging.Debug.dll", "tools/Microsoft.Extensions.Logging.EventLog.dll", "tools/Microsoft.Extensions.Logging.EventSource.dll", "tools/Microsoft.Extensions.Logging.dll", "tools/Microsoft.Extensions.ObjectPool.dll", "tools/Microsoft.Extensions.ObjectPool.xml", "tools/Microsoft.Extensions.Options.ConfigurationExtensions.dll", "tools/Microsoft.Extensions.Options.dll", "tools/Microsoft.Extensions.Primitives.dll", "tools/Microsoft.Extensions.WebEncoders.dll", "tools/Microsoft.Extensions.WebEncoders.xml", "tools/Microsoft.JSInterop.dll", "tools/Microsoft.JSInterop.xml", "tools/Microsoft.Net.Http.Headers.dll", "tools/Microsoft.Net.Http.Headers.xml", "tools/System.Diagnostics.EventLog.dll", "tools/System.IO.Pipelines.dll", "tools/System.Security.Cryptography.Pkcs.dll", "tools/System.Security.Cryptography.Xml.dll", "tools/blazor-devserver.deps.json", "tools/blazor-devserver.dll", "tools/blazor-devserver.exe", "tools/blazor-devserver.runtimeconfig.json", "tools/blazor-devserver.xml", "tools/runtimes/win/lib/net8.0/System.Diagnostics.EventLog.Messages.dll", "tools/runtimes/win/lib/net8.0/System.Diagnostics.EventLog.dll", "tools/runtimes/win/lib/net8.0/System.Security.Cryptography.Pkcs.dll", "tools/x64/aspnetcorev2_inprocess.dll", "tools/x86/aspnetcorev2_inprocess.dll"]}, "Microsoft.AspNetCore.Metadata/8.0.20": {"sha512": "0tULF+2scqnCEDbvd6w6+wU12O3KJgTle3UsrsglJElhXI1w5otkOrfyAKk4UyWhexKUXl99ttXzScP3X3+7gA==", "type": "package", "path": "microsoft.aspnetcore.metadata/8.0.20", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "THIRD-PARTY-NOTICES.TXT", "lib/net462/Microsoft.AspNetCore.Metadata.dll", "lib/net462/Microsoft.AspNetCore.Metadata.xml", "lib/net8.0/Microsoft.AspNetCore.Metadata.dll", "lib/net8.0/Microsoft.AspNetCore.Metadata.xml", "lib/netstandard2.0/Microsoft.AspNetCore.Metadata.dll", "lib/netstandard2.0/Microsoft.AspNetCore.Metadata.xml", "microsoft.aspnetcore.metadata.8.0.20.nupkg.sha512", "microsoft.aspnetcore.metadata.nuspec"]}, "Microsoft.Extensions.Configuration/8.0.0": {"sha512": "0J/9YNXTMWSZP2p2+nvl8p71zpSwokZXZuJW+VjdErkegAnFdO1XlqtA62SJtgVYHdKu3uPxJHcMR/r35HwFBA==", "type": "package", "path": "microsoft.extensions.configuration/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Configuration.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Configuration.targets", "lib/net462/Microsoft.Extensions.Configuration.dll", "lib/net462/Microsoft.Extensions.Configuration.xml", "lib/net6.0/Microsoft.Extensions.Configuration.dll", "lib/net6.0/Microsoft.Extensions.Configuration.xml", "lib/net7.0/Microsoft.Extensions.Configuration.dll", "lib/net7.0/Microsoft.Extensions.Configuration.xml", "lib/net8.0/Microsoft.Extensions.Configuration.dll", "lib/net8.0/Microsoft.Extensions.Configuration.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.xml", "microsoft.extensions.configuration.8.0.0.nupkg.sha512", "microsoft.extensions.configuration.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Configuration.Abstractions/8.0.0": {"sha512": "3lE/iLSutpgX1CC0NOW70FJoGARRHbyKmG7dc0klnUZ9Dd9hS6N/POPWhKhMLCEuNN5nXEY5agmlFtH562vqhQ==", "type": "package", "path": "microsoft.extensions.configuration.abstractions/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Configuration.Abstractions.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Configuration.Abstractions.targets", "lib/net462/Microsoft.Extensions.Configuration.Abstractions.dll", "lib/net462/Microsoft.Extensions.Configuration.Abstractions.xml", "lib/net6.0/Microsoft.Extensions.Configuration.Abstractions.dll", "lib/net6.0/Microsoft.Extensions.Configuration.Abstractions.xml", "lib/net7.0/Microsoft.Extensions.Configuration.Abstractions.dll", "lib/net7.0/Microsoft.Extensions.Configuration.Abstractions.xml", "lib/net8.0/Microsoft.Extensions.Configuration.Abstractions.dll", "lib/net8.0/Microsoft.Extensions.Configuration.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Abstractions.xml", "microsoft.extensions.configuration.abstractions.8.0.0.nupkg.sha512", "microsoft.extensions.configuration.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Configuration.Binder/8.0.2": {"sha512": "7IQhGK+wjyGrNsPBjJcZwWAr+Wf6D4+TwOptUt77bWtgNkiV8tDEbhFS+dDamtQFZ2X7kWG9m71iZQRj2x3zgQ==", "type": "package", "path": "microsoft.extensions.configuration.binder/8.0.2", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "analyzers/dotnet/cs/Microsoft.Extensions.Configuration.Binder.SourceGeneration.dll", "analyzers/dotnet/cs/cs/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/de/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/es/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/fr/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/it/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/ja/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/ko/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/pl/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/pt-BR/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/ru/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/tr/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/zh-<PERSON>/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/zh-Hant/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "buildTransitive/netstandard2.0/Microsoft.Extensions.Configuration.Binder.targets", "lib/net462/Microsoft.Extensions.Configuration.Binder.dll", "lib/net462/Microsoft.Extensions.Configuration.Binder.xml", "lib/net6.0/Microsoft.Extensions.Configuration.Binder.dll", "lib/net6.0/Microsoft.Extensions.Configuration.Binder.xml", "lib/net7.0/Microsoft.Extensions.Configuration.Binder.dll", "lib/net7.0/Microsoft.Extensions.Configuration.Binder.xml", "lib/net8.0/Microsoft.Extensions.Configuration.Binder.dll", "lib/net8.0/Microsoft.Extensions.Configuration.Binder.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Binder.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Binder.xml", "microsoft.extensions.configuration.binder.8.0.2.nupkg.sha512", "microsoft.extensions.configuration.binder.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Configuration.FileExtensions/8.0.1": {"sha512": "EJzSNO9oaAXnTdtdNO6npPRsIIeZCBSNmdQ091VDO7fBiOtJAAeEq6dtrVXIi3ZyjC5XRSAtVvF8SzcneRHqKQ==", "type": "package", "path": "microsoft.extensions.configuration.fileextensions/8.0.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Configuration.FileExtensions.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Configuration.FileExtensions.targets", "lib/net462/Microsoft.Extensions.Configuration.FileExtensions.dll", "lib/net462/Microsoft.Extensions.Configuration.FileExtensions.xml", "lib/net6.0/Microsoft.Extensions.Configuration.FileExtensions.dll", "lib/net6.0/Microsoft.Extensions.Configuration.FileExtensions.xml", "lib/net7.0/Microsoft.Extensions.Configuration.FileExtensions.dll", "lib/net7.0/Microsoft.Extensions.Configuration.FileExtensions.xml", "lib/net8.0/Microsoft.Extensions.Configuration.FileExtensions.dll", "lib/net8.0/Microsoft.Extensions.Configuration.FileExtensions.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.FileExtensions.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.FileExtensions.xml", "microsoft.extensions.configuration.fileextensions.8.0.1.nupkg.sha512", "microsoft.extensions.configuration.fileextensions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Configuration.Json/8.0.1": {"sha512": "L89DLNuimOghjV3tLx0ArFDwVEJD6+uGB3BMCMX01kaLzXkaXHb2021xOMl2QOxUxbdePKUZsUY7n2UUkycjRg==", "type": "package", "path": "microsoft.extensions.configuration.json/8.0.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Configuration.Json.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Configuration.Json.targets", "lib/net462/Microsoft.Extensions.Configuration.Json.dll", "lib/net462/Microsoft.Extensions.Configuration.Json.xml", "lib/net6.0/Microsoft.Extensions.Configuration.Json.dll", "lib/net6.0/Microsoft.Extensions.Configuration.Json.xml", "lib/net7.0/Microsoft.Extensions.Configuration.Json.dll", "lib/net7.0/Microsoft.Extensions.Configuration.Json.xml", "lib/net8.0/Microsoft.Extensions.Configuration.Json.dll", "lib/net8.0/Microsoft.Extensions.Configuration.Json.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Json.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Json.xml", "lib/netstandard2.1/Microsoft.Extensions.Configuration.Json.dll", "lib/netstandard2.1/Microsoft.Extensions.Configuration.Json.xml", "microsoft.extensions.configuration.json.8.0.1.nupkg.sha512", "microsoft.extensions.configuration.json.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.DependencyInjection/8.0.1": {"sha512": "BmANAnR5Xd4Oqw7yQ75xOAYODybZQRzdeNucg7kS5wWKd2PNnMdYtJ2Vciy0QLylRmv42DGl5+AFL9izA6F1Rw==", "type": "package", "path": "microsoft.extensions.dependencyinjection/8.0.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.DependencyInjection.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.DependencyInjection.targets", "lib/net462/Microsoft.Extensions.DependencyInjection.dll", "lib/net462/Microsoft.Extensions.DependencyInjection.xml", "lib/net6.0/Microsoft.Extensions.DependencyInjection.dll", "lib/net6.0/Microsoft.Extensions.DependencyInjection.xml", "lib/net7.0/Microsoft.Extensions.DependencyInjection.dll", "lib/net7.0/Microsoft.Extensions.DependencyInjection.xml", "lib/net8.0/Microsoft.Extensions.DependencyInjection.dll", "lib/net8.0/Microsoft.Extensions.DependencyInjection.xml", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.dll", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.xml", "lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.dll", "lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.xml", "microsoft.extensions.dependencyinjection.8.0.1.nupkg.sha512", "microsoft.extensions.dependencyinjection.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.DependencyInjection.Abstractions/8.0.2": {"sha512": "3iE7UF7MQkCv1cxzCahz+Y/guQbTqieyxyaWKhrRO91itI9cOKO76OHeQDahqG4MmW5umr3CcCvGmK92lWNlbg==", "type": "package", "path": "microsoft.extensions.dependencyinjection.abstractions/8.0.2", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.DependencyInjection.Abstractions.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.DependencyInjection.Abstractions.targets", "lib/net462/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/net462/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/net6.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/net6.0/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/net7.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/net7.0/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/net8.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/net8.0/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "microsoft.extensions.dependencyinjection.abstractions.8.0.2.nupkg.sha512", "microsoft.extensions.dependencyinjection.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.FileProviders.Abstractions/8.0.0": {"sha512": "ZbaMlhJlpisjuWbvXr4LdAst/1XxH3vZ6A0BsgTphZ2L4PGuxRLz7Jr/S7mkAAnOn78Vu0fKhEgNF5JO3zfjqQ==", "type": "package", "path": "microsoft.extensions.fileproviders.abstractions/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.FileProviders.Abstractions.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.FileProviders.Abstractions.targets", "lib/net462/Microsoft.Extensions.FileProviders.Abstractions.dll", "lib/net462/Microsoft.Extensions.FileProviders.Abstractions.xml", "lib/net6.0/Microsoft.Extensions.FileProviders.Abstractions.dll", "lib/net6.0/Microsoft.Extensions.FileProviders.Abstractions.xml", "lib/net8.0/Microsoft.Extensions.FileProviders.Abstractions.dll", "lib/net8.0/Microsoft.Extensions.FileProviders.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.FileProviders.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.FileProviders.Abstractions.xml", "microsoft.extensions.fileproviders.abstractions.8.0.0.nupkg.sha512", "microsoft.extensions.fileproviders.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.FileProviders.Physical/8.0.0": {"sha512": "UboiXxpPUpwulHvIAVE36Knq0VSHaAmfrFkegLyBZeaADuKezJ/AIXYAW8F5GBlGk/VaibN2k/Zn1ca8YAfVdA==", "type": "package", "path": "microsoft.extensions.fileproviders.physical/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.FileProviders.Physical.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.FileProviders.Physical.targets", "lib/net462/Microsoft.Extensions.FileProviders.Physical.dll", "lib/net462/Microsoft.Extensions.FileProviders.Physical.xml", "lib/net6.0/Microsoft.Extensions.FileProviders.Physical.dll", "lib/net6.0/Microsoft.Extensions.FileProviders.Physical.xml", "lib/net7.0/Microsoft.Extensions.FileProviders.Physical.dll", "lib/net7.0/Microsoft.Extensions.FileProviders.Physical.xml", "lib/net8.0/Microsoft.Extensions.FileProviders.Physical.dll", "lib/net8.0/Microsoft.Extensions.FileProviders.Physical.xml", "lib/netstandard2.0/Microsoft.Extensions.FileProviders.Physical.dll", "lib/netstandard2.0/Microsoft.Extensions.FileProviders.Physical.xml", "microsoft.extensions.fileproviders.physical.8.0.0.nupkg.sha512", "microsoft.extensions.fileproviders.physical.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.FileSystemGlobbing/8.0.0": {"sha512": "OK+670i7esqlQrPjdIKRbsyMCe9g5kSLpRRQGSr4Q58AOYEe/hCnfLZprh7viNisSUUQZmMrbbuDaIrP+V1ebQ==", "type": "package", "path": "microsoft.extensions.filesystemglobbing/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.FileSystemGlobbing.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.FileSystemGlobbing.targets", "lib/net462/Microsoft.Extensions.FileSystemGlobbing.dll", "lib/net462/Microsoft.Extensions.FileSystemGlobbing.xml", "lib/net6.0/Microsoft.Extensions.FileSystemGlobbing.dll", "lib/net6.0/Microsoft.Extensions.FileSystemGlobbing.xml", "lib/net7.0/Microsoft.Extensions.FileSystemGlobbing.dll", "lib/net7.0/Microsoft.Extensions.FileSystemGlobbing.xml", "lib/net8.0/Microsoft.Extensions.FileSystemGlobbing.dll", "lib/net8.0/Microsoft.Extensions.FileSystemGlobbing.xml", "lib/netstandard2.0/Microsoft.Extensions.FileSystemGlobbing.dll", "lib/netstandard2.0/Microsoft.Extensions.FileSystemGlobbing.xml", "microsoft.extensions.filesystemglobbing.8.0.0.nupkg.sha512", "microsoft.extensions.filesystemglobbing.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Logging/8.0.1": {"sha512": "4x+pzsQEbqxhNf1QYRr5TDkLP9UsLT3A6MdRKDDEgrW7h1ljiEPgTNhKYUhNCCAaVpQECVQ+onA91PTPnIp6Lw==", "type": "package", "path": "microsoft.extensions.logging/8.0.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Logging.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Logging.targets", "lib/net462/Microsoft.Extensions.Logging.dll", "lib/net462/Microsoft.Extensions.Logging.xml", "lib/net6.0/Microsoft.Extensions.Logging.dll", "lib/net6.0/Microsoft.Extensions.Logging.xml", "lib/net7.0/Microsoft.Extensions.Logging.dll", "lib/net7.0/Microsoft.Extensions.Logging.xml", "lib/net8.0/Microsoft.Extensions.Logging.dll", "lib/net8.0/Microsoft.Extensions.Logging.xml", "lib/netstandard2.0/Microsoft.Extensions.Logging.dll", "lib/netstandard2.0/Microsoft.Extensions.Logging.xml", "lib/netstandard2.1/Microsoft.Extensions.Logging.dll", "lib/netstandard2.1/Microsoft.Extensions.Logging.xml", "microsoft.extensions.logging.8.0.1.nupkg.sha512", "microsoft.extensions.logging.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Logging.Abstractions/8.0.3": {"sha512": "dL0QGToTxggRLMYY4ZYX5AMwBb+byQBd/5dMiZE07Nv73o6I5Are3C7eQTh7K2+A4ct0PVISSr7TZANbiNb2yQ==", "type": "package", "path": "microsoft.extensions.logging.abstractions/8.0.3", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "analyzers/dotnet/roslyn3.11/cs/Microsoft.Extensions.Logging.Generators.dll", "analyzers/dotnet/roslyn3.11/cs/cs/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/de/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/es/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/fr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/it/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ja/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ko/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/pl/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/pt-BR/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ru/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/tr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/zh-<PERSON>/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/zh-Hant/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/Microsoft.Extensions.Logging.Generators.dll", "analyzers/dotnet/roslyn4.0/cs/cs/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/de/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/es/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/fr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/it/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ja/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ko/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/pl/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/pt-BR/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ru/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/tr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/zh-<PERSON>/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/zh-Hant/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/Microsoft.Extensions.Logging.Generators.dll", "analyzers/dotnet/roslyn4.4/cs/cs/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/de/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/es/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/fr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/it/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ja/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ko/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pl/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pt-BR/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ru/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/tr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-<PERSON>/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-Hant/Microsoft.Extensions.Logging.Generators.resources.dll", "buildTransitive/net461/Microsoft.Extensions.Logging.Abstractions.targets", "buildTransitive/net462/Microsoft.Extensions.Logging.Abstractions.targets", "buildTransitive/net6.0/Microsoft.Extensions.Logging.Abstractions.targets", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Logging.Abstractions.targets", "buildTransitive/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.targets", "lib/net462/Microsoft.Extensions.Logging.Abstractions.dll", "lib/net462/Microsoft.Extensions.Logging.Abstractions.xml", "lib/net6.0/Microsoft.Extensions.Logging.Abstractions.dll", "lib/net6.0/Microsoft.Extensions.Logging.Abstractions.xml", "lib/net7.0/Microsoft.Extensions.Logging.Abstractions.dll", "lib/net7.0/Microsoft.Extensions.Logging.Abstractions.xml", "lib/net8.0/Microsoft.Extensions.Logging.Abstractions.dll", "lib/net8.0/Microsoft.Extensions.Logging.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.xml", "microsoft.extensions.logging.abstractions.8.0.3.nupkg.sha512", "microsoft.extensions.logging.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Options/8.0.2": {"sha512": "dWGKvhFybsaZpGmzkGCbNNwBD1rVlWzrZKANLW/CcbFJpCEceMCGzT7zZwHOGBCbwM0SzBuceMj5HN1LKV1QqA==", "type": "package", "path": "microsoft.extensions.options/8.0.2", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "analyzers/dotnet/roslyn4.4/cs/Microsoft.Extensions.Options.SourceGeneration.dll", "analyzers/dotnet/roslyn4.4/cs/cs/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/de/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/es/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/fr/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/it/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ja/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ko/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pl/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pt-BR/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ru/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/tr/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-<PERSON>/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-Hant/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "buildTransitive/net461/Microsoft.Extensions.Options.targets", "buildTransitive/net462/Microsoft.Extensions.Options.targets", "buildTransitive/net6.0/Microsoft.Extensions.Options.targets", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Options.targets", "buildTransitive/netstandard2.0/Microsoft.Extensions.Options.targets", "lib/net462/Microsoft.Extensions.Options.dll", "lib/net462/Microsoft.Extensions.Options.xml", "lib/net6.0/Microsoft.Extensions.Options.dll", "lib/net6.0/Microsoft.Extensions.Options.xml", "lib/net7.0/Microsoft.Extensions.Options.dll", "lib/net7.0/Microsoft.Extensions.Options.xml", "lib/net8.0/Microsoft.Extensions.Options.dll", "lib/net8.0/Microsoft.Extensions.Options.xml", "lib/netstandard2.0/Microsoft.Extensions.Options.dll", "lib/netstandard2.0/Microsoft.Extensions.Options.xml", "lib/netstandard2.1/Microsoft.Extensions.Options.dll", "lib/netstandard2.1/Microsoft.Extensions.Options.xml", "microsoft.extensions.options.8.0.2.nupkg.sha512", "microsoft.extensions.options.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Primitives/8.0.0": {"sha512": "bXJEZrW9ny8vjMF1JV253WeLhpEVzFo1lyaZu1vQ4ZxWUlVvknZ/+ftFgVheLubb4eZPSwwxBeqS1JkCOjxd8g==", "type": "package", "path": "microsoft.extensions.primitives/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Primitives.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Primitives.targets", "lib/net462/Microsoft.Extensions.Primitives.dll", "lib/net462/Microsoft.Extensions.Primitives.xml", "lib/net6.0/Microsoft.Extensions.Primitives.dll", "lib/net6.0/Microsoft.Extensions.Primitives.xml", "lib/net7.0/Microsoft.Extensions.Primitives.dll", "lib/net7.0/Microsoft.Extensions.Primitives.xml", "lib/net8.0/Microsoft.Extensions.Primitives.dll", "lib/net8.0/Microsoft.Extensions.Primitives.xml", "lib/netstandard2.0/Microsoft.Extensions.Primitives.dll", "lib/netstandard2.0/Microsoft.Extensions.Primitives.xml", "microsoft.extensions.primitives.8.0.0.nupkg.sha512", "microsoft.extensions.primitives.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.JSInterop/8.0.20": {"sha512": "0jdxTuYqGKmPVuiMMzz0G3k0X/hcrXWm8H1kPFzQQHoN72DDZJ+os/i2yM7CWznnvAdRkSNfCKnYJt7Q7Qhr0g==", "type": "package", "path": "microsoft.jsinterop/8.0.20", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "THIRD-PARTY-NOTICES.TXT", "lib/net8.0/Microsoft.JSInterop.dll", "lib/net8.0/Microsoft.JSInterop.xml", "microsoft.jsinterop.8.0.20.nupkg.sha512", "microsoft.jsinterop.nuspec"]}, "Microsoft.JSInterop.WebAssembly/8.0.20": {"sha512": "MP/3NQ7b6gsHiDO0BpV1QbKffXNaE4RfAy9gBSep9GZ8+Ao2fifkHRXZqzoq3Ns1o2QVz8dukPNWrqefjibpzA==", "type": "package", "path": "microsoft.jsinterop.webassembly/8.0.20", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "THIRD-PARTY-NOTICES.txt", "lib/net8.0/Microsoft.JSInterop.WebAssembly.dll", "lib/net8.0/Microsoft.JSInterop.WebAssembly.xml", "microsoft.jsinterop.webassembly.8.0.20.nupkg.sha512", "microsoft.jsinterop.webassembly.nuspec"]}, "Microsoft.NET.ILLink.Tasks/8.0.20": {"sha512": "Rhcto2AjGvTO62+/VTmBpumBOmqIGp7nYEbTbmEXkCq4yPGxV8whju3/HsIA/bKyo2+DggaYk5+/8sxb1AbPTw==", "type": "package", "path": "microsoft.net.illink.tasks/8.0.20", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "Sdk/Sdk.props", "THIRD-PARTY-NOTICES.TXT", "analyzers/dotnet/cs/ILLink.CodeFixProvider.dll", "analyzers/dotnet/cs/ILLink.RoslynAnalyzer.dll", "build/Microsoft.NET.ILLink.Analyzers.props", "build/Microsoft.NET.ILLink.Tasks.props", "build/Microsoft.NET.ILLink.targets", "microsoft.net.illink.tasks.8.0.20.nupkg.sha512", "microsoft.net.illink.tasks.nuspec", "tools/net472/ILLink.Tasks.dll", "tools/net472/ILLink.Tasks.dll.config", "tools/net472/Mono.Cecil.Mdb.dll", "tools/net472/Mono.Cecil.Pdb.dll", "tools/net472/Mono.Cecil.Rocks.dll", "tools/net472/Mono.Cecil.dll", "tools/net472/Sdk/Sdk.props", "tools/net472/System.Buffers.dll", "tools/net472/System.Collections.Immutable.dll", "tools/net472/System.Memory.dll", "tools/net472/System.Numerics.Vectors.dll", "tools/net472/System.Reflection.Metadata.dll", "tools/net472/System.Runtime.CompilerServices.Unsafe.dll", "tools/net472/build/Microsoft.NET.ILLink.Analyzers.props", "tools/net472/build/Microsoft.NET.ILLink.Tasks.props", "tools/net472/build/Microsoft.NET.ILLink.targets", "tools/net8.0/ILLink.Tasks.deps.json", "tools/net8.0/ILLink.Tasks.dll", "tools/net8.0/Mono.Cecil.Mdb.dll", "tools/net8.0/Mono.Cecil.Pdb.dll", "tools/net8.0/Mono.Cecil.Rocks.dll", "tools/net8.0/Mono.Cecil.dll", "tools/net8.0/Sdk/Sdk.props", "tools/net8.0/build/Microsoft.NET.ILLink.Analyzers.props", "tools/net8.0/build/Microsoft.NET.ILLink.Tasks.props", "tools/net8.0/build/Microsoft.NET.ILLink.targets", "tools/net8.0/illink.deps.json", "tools/net8.0/illink.dll", "tools/net8.0/illink.runtimeconfig.json", "useSharedDesignerContext.txt"]}, "Microsoft.NET.Sdk.WebAssembly.Pack/8.0.20": {"sha512": "T8IgvEg6M4INrHw9K89JjslxP0D6fHYiogFvarNPSjfusHdV/KvwJU4YC1+lNOIIgpjslkb4WWSW9IuNxHdPLg==", "type": "package", "path": "microsoft.net.sdk.webassembly.pack/8.0.20", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "WasmAppHost/BrowserDebugHost.deps.json", "WasmAppHost/BrowserDebugHost.dll", "WasmAppHost/BrowserDebugHost.runtimeconfig.json", "WasmAppHost/BrowserDebugProxy.dll", "WasmAppHost/Microsoft.CodeAnalysis.CSharp.Scripting.dll", "WasmAppHost/Microsoft.CodeAnalysis.CSharp.dll", "WasmAppHost/Microsoft.CodeAnalysis.Scripting.dll", "WasmAppHost/Microsoft.CodeAnalysis.dll", "WasmAppHost/Microsoft.FileFormats.dll", "WasmAppHost/Microsoft.NET.WebAssembly.Webcil.dll", "WasmAppHost/Microsoft.SymbolStore.dll", "WasmAppHost/Newtonsoft.Json.dll", "WasmAppHost/WasmAppHost.deps.json", "WasmAppHost/WasmAppHost.dll", "WasmAppHost/WasmAppHost.runtimeconfig.json", "build/Microsoft.NET.Sdk.WebAssembly.Browser.props", "build/Microsoft.NET.Sdk.WebAssembly.Browser.targets", "build/Microsoft.NET.Sdk.WebAssembly.Pack.props", "build/Microsoft.NET.Sdk.WebAssembly.Pack.targets", "build/Wasm.web.config", "microsoft.net.sdk.webassembly.pack.8.0.20.nupkg.sha512", "microsoft.net.sdk.webassembly.pack.nuspec", "tools/net472/Microsoft.NET.Sdk.WebAssembly.Pack.Tasks.dll", "tools/net472/Microsoft.NET.WebAssembly.Webcil.dll", "tools/net8.0/Microsoft.NET.Sdk.WebAssembly.Pack.Tasks.dll", "tools/net8.0/Microsoft.NET.WebAssembly.Webcil.dll"]}, "System.IO.Pipelines/8.0.0": {"sha512": "FHNOatmUq0sqJOkTx+UF/9YK1f180cnW5FVqnQMvYUN0elp6wFzbtPSiqbo1/ru8ICp43JM1i7kKkk6GsNGHlA==", "type": "package", "path": "system.io.pipelines/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.IO.Pipelines.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.IO.Pipelines.targets", "lib/net462/System.IO.Pipelines.dll", "lib/net462/System.IO.Pipelines.xml", "lib/net6.0/System.IO.Pipelines.dll", "lib/net6.0/System.IO.Pipelines.xml", "lib/net7.0/System.IO.Pipelines.dll", "lib/net7.0/System.IO.Pipelines.xml", "lib/net8.0/System.IO.Pipelines.dll", "lib/net8.0/System.IO.Pipelines.xml", "lib/netstandard2.0/System.IO.Pipelines.dll", "lib/netstandard2.0/System.IO.Pipelines.xml", "system.io.pipelines.8.0.0.nupkg.sha512", "system.io.pipelines.nuspec", "useSharedDesignerContext.txt"]}, "UserManagement.Shared/1.0.0": {"type": "project", "path": "../../UserManagement.Shared/UserManagement.Shared/UserManagement.Shared.csproj", "msbuildProject": "../../UserManagement.Shared/UserManagement.Shared/UserManagement.Shared.csproj"}}, "projectFileDependencyGroups": {"net8.0": ["Microsoft.AspNetCore.Components.WebAssembly >= 8.0.20", "Microsoft.AspNetCore.Components.WebAssembly.DevServer >= 8.0.20", "Microsoft.NET.ILLink.Tasks >= 8.0.20", "Microsoft.NET.Sdk.WebAssembly.Pack >= 8.0.20", "UserManagement.Shared >= 1.0.0"]}, "packageFolders": {"/home/<USER>/.nuget/packages/": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/UserManagement.Client.csproj", "projectName": "UserManagement.Client", "projectPath": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/UserManagement.Client.csproj", "packagesPath": "/home/<USER>/.nuget/packages/", "outputPath": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/obj/", "projectStyle": "PackageReference", "configFilePaths": ["/home/<USER>/.nuget/NuGet/NuGet.Config"], "originalTargetFrameworks": ["net8.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Shared/UserManagement.Shared/UserManagement.Shared.csproj": {"projectPath": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Shared/UserManagement.Shared/UserManagement.Shared.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Microsoft.AspNetCore.Components.WebAssembly": {"target": "Package", "version": "[8.0.20, )"}, "Microsoft.AspNetCore.Components.WebAssembly.DevServer": {"suppressParent": "All", "target": "Package", "version": "[8.0.20, )"}, "Microsoft.NET.ILLink.Tasks": {"suppressParent": "All", "target": "Package", "version": "[8.0.20, )", "autoReferenced": true}, "Microsoft.NET.Sdk.WebAssembly.Pack": {"suppressParent": "All", "target": "Package", "version": "[8.0.20, )", "autoReferenced": true}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.NETCore.App.Runtime.Mono.browser-wasm", "version": "[8.0.20, 8.0.20]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "/usr/share/dotnet/sdk/8.0.414/PortableRuntimeIdentifierGraph.json"}}, "runtimes": {"browser-wasm": {"#import": []}}}}