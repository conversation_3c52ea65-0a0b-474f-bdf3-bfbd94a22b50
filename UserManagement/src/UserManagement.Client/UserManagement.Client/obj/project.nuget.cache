{"version": 2, "dgSpecHash": "pJypISP28D0=", "success": true, "projectFilePath": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Client/UserManagement.Client/UserManagement.Client.csproj", "expectedPackageFiles": ["/home/<USER>/.nuget/packages/microsoft.aspnetcore.authorization/8.0.20/microsoft.aspnetcore.authorization.8.0.20.nupkg.sha512", "/home/<USER>/.nuget/packages/microsoft.aspnetcore.components/8.0.20/microsoft.aspnetcore.components.8.0.20.nupkg.sha512", "/home/<USER>/.nuget/packages/microsoft.aspnetcore.components.analyzers/8.0.20/microsoft.aspnetcore.components.analyzers.8.0.20.nupkg.sha512", "/home/<USER>/.nuget/packages/microsoft.aspnetcore.components.forms/8.0.20/microsoft.aspnetcore.components.forms.8.0.20.nupkg.sha512", "/home/<USER>/.nuget/packages/microsoft.aspnetcore.components.web/8.0.20/microsoft.aspnetcore.components.web.8.0.20.nupkg.sha512", "/home/<USER>/.nuget/packages/microsoft.aspnetcore.components.webassembly/8.0.20/microsoft.aspnetcore.components.webassembly.8.0.20.nupkg.sha512", "/home/<USER>/.nuget/packages/microsoft.aspnetcore.components.webassembly.devserver/8.0.20/microsoft.aspnetcore.components.webassembly.devserver.8.0.20.nupkg.sha512", "/home/<USER>/.nuget/packages/microsoft.aspnetcore.metadata/8.0.20/microsoft.aspnetcore.metadata.8.0.20.nupkg.sha512", "/home/<USER>/.nuget/packages/microsoft.extensions.configuration/8.0.0/microsoft.extensions.configuration.8.0.0.nupkg.sha512", "/home/<USER>/.nuget/packages/microsoft.extensions.configuration.abstractions/8.0.0/microsoft.extensions.configuration.abstractions.8.0.0.nupkg.sha512", "/home/<USER>/.nuget/packages/microsoft.extensions.configuration.binder/8.0.2/microsoft.extensions.configuration.binder.8.0.2.nupkg.sha512", "/home/<USER>/.nuget/packages/microsoft.extensions.configuration.fileextensions/8.0.1/microsoft.extensions.configuration.fileextensions.8.0.1.nupkg.sha512", "/home/<USER>/.nuget/packages/microsoft.extensions.configuration.json/8.0.1/microsoft.extensions.configuration.json.8.0.1.nupkg.sha512", "/home/<USER>/.nuget/packages/microsoft.extensions.dependencyinjection/8.0.1/microsoft.extensions.dependencyinjection.8.0.1.nupkg.sha512", "/home/<USER>/.nuget/packages/microsoft.extensions.dependencyinjection.abstractions/8.0.2/microsoft.extensions.dependencyinjection.abstractions.8.0.2.nupkg.sha512", "/home/<USER>/.nuget/packages/microsoft.extensions.fileproviders.abstractions/8.0.0/microsoft.extensions.fileproviders.abstractions.8.0.0.nupkg.sha512", "/home/<USER>/.nuget/packages/microsoft.extensions.fileproviders.physical/8.0.0/microsoft.extensions.fileproviders.physical.8.0.0.nupkg.sha512", "/home/<USER>/.nuget/packages/microsoft.extensions.filesystemglobbing/8.0.0/microsoft.extensions.filesystemglobbing.8.0.0.nupkg.sha512", "/home/<USER>/.nuget/packages/microsoft.extensions.logging/8.0.1/microsoft.extensions.logging.8.0.1.nupkg.sha512", "/home/<USER>/.nuget/packages/microsoft.extensions.logging.abstractions/8.0.3/microsoft.extensions.logging.abstractions.8.0.3.nupkg.sha512", "/home/<USER>/.nuget/packages/microsoft.extensions.options/8.0.2/microsoft.extensions.options.8.0.2.nupkg.sha512", "/home/<USER>/.nuget/packages/microsoft.extensions.primitives/8.0.0/microsoft.extensions.primitives.8.0.0.nupkg.sha512", "/home/<USER>/.nuget/packages/microsoft.jsinterop/8.0.20/microsoft.jsinterop.8.0.20.nupkg.sha512", "/home/<USER>/.nuget/packages/microsoft.jsinterop.webassembly/8.0.20/microsoft.jsinterop.webassembly.8.0.20.nupkg.sha512", "/home/<USER>/.nuget/packages/microsoft.net.illink.tasks/8.0.20/microsoft.net.illink.tasks.8.0.20.nupkg.sha512", "/home/<USER>/.nuget/packages/microsoft.net.sdk.webassembly.pack/8.0.20/microsoft.net.sdk.webassembly.pack.8.0.20.nupkg.sha512", "/home/<USER>/.nuget/packages/system.io.pipelines/8.0.0/system.io.pipelines.8.0.0.nupkg.sha512", "/home/<USER>/.nuget/packages/microsoft.netcore.app.runtime.mono.browser-wasm/8.0.20/microsoft.netcore.app.runtime.mono.browser-wasm.8.0.20.nupkg.sha512"], "logs": []}