using Microsoft.AspNetCore.Mvc;
using UserManagement.Shared;

namespace UserManagement.Host.Controllers;

/// <summary>
/// Welcome controller for the modular monolith
/// </summary>
[ApiController]
[Route("api/[controller]")]
public class WelcomeController : ControllerBase
{
    /// <summary>
    /// Gets welcome message
    /// </summary>
    /// <returns>Welcome message</returns>
    [HttpGet]
    public IActionResult GetWelcomeMessage()
    {
        var welcomeMessage = new
        {
            Message = $"Welcome to {SharedConstants.ApplicationName}",
            Version = SharedConstants.ApplicationVersion,
            Timestamp = DateTime.UtcNow,
            Architecture = "Modular Monolithic",
            Modules = new[]
            {
                SharedConstants.Modules.Users,
                SharedConstants.Modules.Sites,
                SharedConstants.Modules.Applications,
                SharedConstants.Modules.Organizations,
                SharedConstants.Modules.Auth
            },
            Features = new[]
            {
                "Clean Architecture",
                "CQRS Pattern",
                "Repository Pattern",
                "Blazor WebAssembly",
                "Modular Design",
                "Microservices Ready"
            }
        };

        return Ok(welcomeMessage);
    }

    /// <summary>
    /// Gets system information
    /// </summary>
    /// <returns>System information</returns>
    [HttpGet("system-info")]
    public IActionResult GetSystemInfo()
    {
        var systemInfo = new
        {
            Environment = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") ?? "Development",
            MachineName = Environment.MachineName,
            ProcessId = Environment.ProcessId,
            WorkingSet = Environment.WorkingSet,
            ProcessorCount = Environment.ProcessorCount,
            OSVersion = Environment.OSVersion.ToString(),
            FrameworkVersion = Environment.Version.ToString(),
            ApplicationName = SharedConstants.ApplicationName,
            ApplicationVersion = SharedConstants.ApplicationVersion
        };

        return Ok(systemInfo);
    }

    /// <summary>
    /// Gets module information
    /// </summary>
    /// <returns>Module information</returns>
    [HttpGet("modules")]
    public IActionResult GetModules()
    {
        var modules = new[]
        {
            new { Name = "Users", Route = SharedConstants.ApiRoutes.Users, Status = "Ready" },
            new { Name = "Sites", Route = SharedConstants.ApiRoutes.Sites, Status = "Ready" },
            new { Name = "Applications", Route = SharedConstants.ApiRoutes.Applications, Status = "Ready" },
            new { Name = "Organizations", Route = SharedConstants.ApiRoutes.Organizations, Status = "Ready" },
            new { Name = "Auth", Route = SharedConstants.ApiRoutes.Auth, Status = "Ready" }
        };

        return Ok(modules);
    }
}
