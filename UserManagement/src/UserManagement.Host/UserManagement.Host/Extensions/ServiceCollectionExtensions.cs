using Microsoft.Extensions.DependencyInjection;

namespace UserManagement.Host.Extensions;

/// <summary>
/// Extension methods for service registration
/// </summary>
public static class ServiceCollectionExtensions
{
    /// <summary>
    /// Registers all module services
    /// </summary>
    /// <param name="services">The service collection</param>
    /// <returns>The service collection for chaining</returns>
    public static IServiceCollection RegisterModuleServices(this IServiceCollection services)
    {
        // Register Users Module
        services.AddUserManagementModule();
        
        // Register Sites Module
        services.AddSitesModule();
        
        // Register Applications Module
        services.AddApplicationsModule();
        
        // Register Organizations Module
        services.AddOrganizationsModule();
        
        // Register Auth Module
        services.AddAuthModule();

        return services;
    }

    /// <summary>
    /// Registers Users module services
    /// </summary>
    private static IServiceCollection AddUserManagementModule(this IServiceCollection services)
    {
        // Register Users module services
        services.AddScoped<UserManagement.Modules.Users.Domain.Interfaces.IUserRepository, 
                           UserManagement.Modules.Users.Infrastructure.Repositories.InMemoryUserRepository>();
        
        return services;
    }

    /// <summary>
    /// Registers Sites module services
    /// </summary>
    private static IServiceCollection AddSitesModule(this IServiceCollection services)
    {
        // TODO: Register Sites module services when implemented
        return services;
    }

    /// <summary>
    /// Registers Applications module services
    /// </summary>
    private static IServiceCollection AddApplicationsModule(this IServiceCollection services)
    {
        // TODO: Register Applications module services when implemented
        return services;
    }

    /// <summary>
    /// Registers Organizations module services
    /// </summary>
    private static IServiceCollection AddOrganizationsModule(this IServiceCollection services)
    {
        // TODO: Register Organizations module services when implemented
        return services;
    }

    /// <summary>
    /// Registers Auth module services
    /// </summary>
    private static IServiceCollection AddAuthModule(this IServiceCollection services)
    {
        // TODO: Register Auth module services when implemented
        return services;
    }
}
