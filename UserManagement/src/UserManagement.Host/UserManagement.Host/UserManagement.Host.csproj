<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.AspNetCore.OpenApi" Version="8.0.20" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="9.0.4" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\UserManagement.Shared\UserManagement.Shared\UserManagement.Shared.csproj" />
    <ProjectReference Include="..\..\Modules\UserManagement.Modules.Users\UserManagement.Modules.Users\UserManagement.Modules.Users.csproj" />
    <ProjectReference Include="..\..\Modules\UserManagement.Modules.Sites\UserManagement.Modules.Sites\UserManagement.Modules.Sites.csproj" />
    <ProjectReference Include="..\..\Modules\UserManagement.Modules.Applications\UserManagement.Modules.Applications\UserManagement.Modules.Applications.csproj" />
    <ProjectReference Include="..\..\Modules\UserManagement.Modules.Organizations\UserManagement.Modules.Organizations\UserManagement.Modules.Organizations.csproj" />
    <ProjectReference Include="..\..\Modules\UserManagement.Modules.Auth\UserManagement.Modules.Auth\UserManagement.Modules.Auth.csproj" />
  </ItemGroup>

</Project>
