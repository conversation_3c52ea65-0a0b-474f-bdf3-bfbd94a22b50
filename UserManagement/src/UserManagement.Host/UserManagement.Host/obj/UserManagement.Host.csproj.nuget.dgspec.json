{"format": 1, "restore": {"/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Host/UserManagement.Host/UserManagement.Host.csproj": {}}, "projects": {"/home/<USER>/Documents/dot net 8/UserManagement/src/Modules/UserManagement.Modules.Applications/UserManagement.Modules.Applications/UserManagement.Modules.Applications.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "/home/<USER>/Documents/dot net 8/UserManagement/src/Modules/UserManagement.Modules.Applications/UserManagement.Modules.Applications/UserManagement.Modules.Applications.csproj", "projectName": "UserManagement.Modules.Applications", "projectPath": "/home/<USER>/Documents/dot net 8/UserManagement/src/Modules/UserManagement.Modules.Applications/UserManagement.Modules.Applications/UserManagement.Modules.Applications.csproj", "packagesPath": "/home/<USER>/.nuget/packages/", "outputPath": "/home/<USER>/Documents/dot net 8/UserManagement/src/Modules/UserManagement.Modules.Applications/UserManagement.Modules.Applications/obj/", "projectStyle": "PackageReference", "configFilePaths": ["/home/<USER>/.nuget/NuGet/NuGet.Config"], "originalTargetFrameworks": ["net8.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Shared/UserManagement.Shared/UserManagement.Shared.csproj": {"projectPath": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Shared/UserManagement.Shared/UserManagement.Shared.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "/usr/share/dotnet/sdk/8.0.414/PortableRuntimeIdentifierGraph.json"}}}, "/home/<USER>/Documents/dot net 8/UserManagement/src/Modules/UserManagement.Modules.Auth/UserManagement.Modules.Auth/UserManagement.Modules.Auth.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "/home/<USER>/Documents/dot net 8/UserManagement/src/Modules/UserManagement.Modules.Auth/UserManagement.Modules.Auth/UserManagement.Modules.Auth.csproj", "projectName": "UserManagement.Modules.Auth", "projectPath": "/home/<USER>/Documents/dot net 8/UserManagement/src/Modules/UserManagement.Modules.Auth/UserManagement.Modules.Auth/UserManagement.Modules.Auth.csproj", "packagesPath": "/home/<USER>/.nuget/packages/", "outputPath": "/home/<USER>/Documents/dot net 8/UserManagement/src/Modules/UserManagement.Modules.Auth/UserManagement.Modules.Auth/obj/", "projectStyle": "PackageReference", "configFilePaths": ["/home/<USER>/.nuget/NuGet/NuGet.Config"], "originalTargetFrameworks": ["net8.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Shared/UserManagement.Shared/UserManagement.Shared.csproj": {"projectPath": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Shared/UserManagement.Shared/UserManagement.Shared.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "/usr/share/dotnet/sdk/8.0.414/PortableRuntimeIdentifierGraph.json"}}}, "/home/<USER>/Documents/dot net 8/UserManagement/src/Modules/UserManagement.Modules.Organizations/UserManagement.Modules.Organizations/UserManagement.Modules.Organizations.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "/home/<USER>/Documents/dot net 8/UserManagement/src/Modules/UserManagement.Modules.Organizations/UserManagement.Modules.Organizations/UserManagement.Modules.Organizations.csproj", "projectName": "UserManagement.Modules.Organizations", "projectPath": "/home/<USER>/Documents/dot net 8/UserManagement/src/Modules/UserManagement.Modules.Organizations/UserManagement.Modules.Organizations/UserManagement.Modules.Organizations.csproj", "packagesPath": "/home/<USER>/.nuget/packages/", "outputPath": "/home/<USER>/Documents/dot net 8/UserManagement/src/Modules/UserManagement.Modules.Organizations/UserManagement.Modules.Organizations/obj/", "projectStyle": "PackageReference", "configFilePaths": ["/home/<USER>/.nuget/NuGet/NuGet.Config"], "originalTargetFrameworks": ["net8.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Shared/UserManagement.Shared/UserManagement.Shared.csproj": {"projectPath": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Shared/UserManagement.Shared/UserManagement.Shared.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "/usr/share/dotnet/sdk/8.0.414/PortableRuntimeIdentifierGraph.json"}}}, "/home/<USER>/Documents/dot net 8/UserManagement/src/Modules/UserManagement.Modules.Sites/UserManagement.Modules.Sites/UserManagement.Modules.Sites.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "/home/<USER>/Documents/dot net 8/UserManagement/src/Modules/UserManagement.Modules.Sites/UserManagement.Modules.Sites/UserManagement.Modules.Sites.csproj", "projectName": "UserManagement.Modules.Sites", "projectPath": "/home/<USER>/Documents/dot net 8/UserManagement/src/Modules/UserManagement.Modules.Sites/UserManagement.Modules.Sites/UserManagement.Modules.Sites.csproj", "packagesPath": "/home/<USER>/.nuget/packages/", "outputPath": "/home/<USER>/Documents/dot net 8/UserManagement/src/Modules/UserManagement.Modules.Sites/UserManagement.Modules.Sites/obj/", "projectStyle": "PackageReference", "configFilePaths": ["/home/<USER>/.nuget/NuGet/NuGet.Config"], "originalTargetFrameworks": ["net8.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Shared/UserManagement.Shared/UserManagement.Shared.csproj": {"projectPath": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Shared/UserManagement.Shared/UserManagement.Shared.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "/usr/share/dotnet/sdk/8.0.414/PortableRuntimeIdentifierGraph.json"}}}, "/home/<USER>/Documents/dot net 8/UserManagement/src/Modules/UserManagement.Modules.Users/UserManagement.Modules.Users/UserManagement.Modules.Users.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "/home/<USER>/Documents/dot net 8/UserManagement/src/Modules/UserManagement.Modules.Users/UserManagement.Modules.Users/UserManagement.Modules.Users.csproj", "projectName": "UserManagement.Modules.Users", "projectPath": "/home/<USER>/Documents/dot net 8/UserManagement/src/Modules/UserManagement.Modules.Users/UserManagement.Modules.Users/UserManagement.Modules.Users.csproj", "packagesPath": "/home/<USER>/.nuget/packages/", "outputPath": "/home/<USER>/Documents/dot net 8/UserManagement/src/Modules/UserManagement.Modules.Users/UserManagement.Modules.Users/obj/", "projectStyle": "PackageReference", "configFilePaths": ["/home/<USER>/.nuget/NuGet/NuGet.Config"], "originalTargetFrameworks": ["net8.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Shared/UserManagement.Shared/UserManagement.Shared.csproj": {"projectPath": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Shared/UserManagement.Shared/UserManagement.Shared.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Microsoft.AspNetCore.Mvc.Core": {"target": "Package", "version": "[2.3.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "/usr/share/dotnet/sdk/8.0.414/PortableRuntimeIdentifierGraph.json"}}}, "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Host/UserManagement.Host/UserManagement.Host.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Host/UserManagement.Host/UserManagement.Host.csproj", "projectName": "UserManagement.Host", "projectPath": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Host/UserManagement.Host/UserManagement.Host.csproj", "packagesPath": "/home/<USER>/.nuget/packages/", "outputPath": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Host/UserManagement.Host/obj/", "projectStyle": "PackageReference", "configFilePaths": ["/home/<USER>/.nuget/NuGet/NuGet.Config"], "originalTargetFrameworks": ["net8.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"/home/<USER>/Documents/dot net 8/UserManagement/src/Modules/UserManagement.Modules.Applications/UserManagement.Modules.Applications/UserManagement.Modules.Applications.csproj": {"projectPath": "/home/<USER>/Documents/dot net 8/UserManagement/src/Modules/UserManagement.Modules.Applications/UserManagement.Modules.Applications/UserManagement.Modules.Applications.csproj"}, "/home/<USER>/Documents/dot net 8/UserManagement/src/Modules/UserManagement.Modules.Auth/UserManagement.Modules.Auth/UserManagement.Modules.Auth.csproj": {"projectPath": "/home/<USER>/Documents/dot net 8/UserManagement/src/Modules/UserManagement.Modules.Auth/UserManagement.Modules.Auth/UserManagement.Modules.Auth.csproj"}, "/home/<USER>/Documents/dot net 8/UserManagement/src/Modules/UserManagement.Modules.Organizations/UserManagement.Modules.Organizations/UserManagement.Modules.Organizations.csproj": {"projectPath": "/home/<USER>/Documents/dot net 8/UserManagement/src/Modules/UserManagement.Modules.Organizations/UserManagement.Modules.Organizations/UserManagement.Modules.Organizations.csproj"}, "/home/<USER>/Documents/dot net 8/UserManagement/src/Modules/UserManagement.Modules.Sites/UserManagement.Modules.Sites/UserManagement.Modules.Sites.csproj": {"projectPath": "/home/<USER>/Documents/dot net 8/UserManagement/src/Modules/UserManagement.Modules.Sites/UserManagement.Modules.Sites/UserManagement.Modules.Sites.csproj"}, "/home/<USER>/Documents/dot net 8/UserManagement/src/Modules/UserManagement.Modules.Users/UserManagement.Modules.Users/UserManagement.Modules.Users.csproj": {"projectPath": "/home/<USER>/Documents/dot net 8/UserManagement/src/Modules/UserManagement.Modules.Users/UserManagement.Modules.Users/UserManagement.Modules.Users.csproj"}, "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Shared/UserManagement.Shared/UserManagement.Shared.csproj": {"projectPath": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Shared/UserManagement.Shared/UserManagement.Shared.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Microsoft.AspNetCore.OpenApi": {"target": "Package", "version": "[8.0.20, )"}, "Swashbuckle.AspNetCore": {"target": "Package", "version": "[9.0.4, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "/usr/share/dotnet/sdk/8.0.414/PortableRuntimeIdentifierGraph.json"}}}, "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Shared/UserManagement.Shared/UserManagement.Shared.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Shared/UserManagement.Shared/UserManagement.Shared.csproj", "projectName": "UserManagement.Shared", "projectPath": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Shared/UserManagement.Shared/UserManagement.Shared.csproj", "packagesPath": "/home/<USER>/.nuget/packages/", "outputPath": "/home/<USER>/Documents/dot net 8/UserManagement/src/UserManagement.Shared/UserManagement.Shared/obj/", "projectStyle": "PackageReference", "configFilePaths": ["/home/<USER>/.nuget/NuGet/NuGet.Config"], "originalTargetFrameworks": ["net8.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "/usr/share/dotnet/sdk/8.0.414/PortableRuntimeIdentifierGraph.json"}}}}}