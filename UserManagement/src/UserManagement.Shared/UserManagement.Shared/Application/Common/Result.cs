namespace UserManagement.Shared.Application.Common;

/// <summary>
/// Generic result class for operation outcomes
/// </summary>
/// <typeparam name="T">The type of data returned</typeparam>
public class Result<T>
{
    /// <summary>
    /// Gets or sets a value indicating whether the operation was successful
    /// </summary>
    public bool IsSuccess { get; set; }

    /// <summary>
    /// Gets or sets the data returned by the operation
    /// </summary>
    public T? Data { get; set; }

    /// <summary>
    /// Gets or sets the error message if the operation failed
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// Gets or sets the list of validation errors
    /// </summary>
    public List<string> ValidationErrors { get; set; } = new();

    /// <summary>
    /// Creates a successful result
    /// </summary>
    /// <param name="data">The data to return</param>
    /// <returns>A successful result</returns>
    public static Result<T> Success(T data)
    {
        return new Result<T>
        {
            IsSuccess = true,
            Data = data
        };
    }

    /// <summary>
    /// Creates a failed result
    /// </summary>
    /// <param name="errorMessage">The error message</param>
    /// <returns>A failed result</returns>
    public static Result<T> Failure(string errorMessage)
    {
        return new Result<T>
        {
            IsSuccess = false,
            ErrorMessage = errorMessage
        };
    }

    /// <summary>
    /// Creates a failed result with validation errors
    /// </summary>
    /// <param name="validationErrors">The validation errors</param>
    /// <returns>A failed result</returns>
    public static Result<T> ValidationFailure(List<string> validationErrors)
    {
        return new Result<T>
        {
            IsSuccess = false,
            ValidationErrors = validationErrors
        };
    }
}
