namespace UserManagement.Shared;

/// <summary>
/// Shared constants across the application
/// </summary>
public static class SharedConstants
{
    /// <summary>
    /// Application name
    /// </summary>
    public const string ApplicationName = "User Management System";

    /// <summary>
    /// Application version
    /// </summary>
    public const string ApplicationVersion = "1.0.0";

    /// <summary>
    /// Default page size for pagination
    /// </summary>
    public const int DefaultPageSize = 10;

    /// <summary>
    /// Maximum page size for pagination
    /// </summary>
    public const int MaxPageSize = 100;

    /// <summary>
    /// API routes
    /// </summary>
    public static class ApiRoutes
    {
        public const string Users = "api/users";
        public const string Sites = "api/sites";
        public const string Applications = "api/applications";
        public const string Organizations = "api/organizations";
        public const string Auth = "api/auth";
    }

    /// <summary>
    /// Module names
    /// </summary>
    public static class Modules
    {
        public const string Users = "UserManagement.Modules.Users";
        public const string Sites = "UserManagement.Modules.Sites";
        public const string Applications = "UserManagement.Modules.Applications";
        public const string Organizations = "UserManagement.Modules.Organizations";
        public const string Auth = "UserManagement.Modules.Auth";
    }
}
